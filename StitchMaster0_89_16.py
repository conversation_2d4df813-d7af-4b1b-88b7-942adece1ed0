import sys
import datetime
from PySide6.QtWidgets import (QApplication, QMainWindow, QToolBar, QLabel, QStatusBar, 
    QHBoxLayout, QWidget, QFileDialog, QMessageBox, QListWidget, QListWidgetItem, 
    QInputDialog, QLineEdit, QPushButton, QComboBox, QVBoxLayout, QSlider, 
    QDialog, QTableWidget, QHeaderView, QTableWidgetItem, QCheckBox, 
    QDialogButtonBox, QScrollArea, QFormLayout, QGridLayout, QGraphicsView, QGraphicsScene, 
    QGraphicsLineItem, QGroupBox, QRadioButton, QSpinBox, QFrame) 
from PySide6.QtGui import (QIcon, QAction, QPixmap, QImage, QIntValidator, 
                           QCursor, QPen, QMouseEvent, QPainter, QFont, QColor, QScreen)
from PySide6.QtCore import Qt, Signal, Slot, QPointF, QLineF, QPoint,QEvent
from PySide6.QtSvgWidgets import QSvgWidget

from dataclasses import dataclass
from typing import List, Optional, Dict
from PySide6.QtCore import QPointF
import cv2
import numpy as np
import pickle
import brands as br
import json
import os
from pathlib import Path
from svglib.svglib import svg2rlg
from reportlab.graphics import renderPM, renderPDF
from cairosvg import svg2png
#import svgwrite
import math
import shutil
from PIL import Image
import traceback
import random
import colorsys
#import re
#from io import StringIO
#import pycairo
#import pdf2image
from psd_tools import PSDImage
# python3 -m venv .venv
# source venv/bin/activate

# Global Variables
"""
#VERSION="v0.1 Initial Gui Prototype"
#VERSION="v0.2 Extract Floss Class"
#VERSION="v0.3 Put flosses under brands Class"
#VERSION="v0.6 Reduce colours for main window"
#VERSION="v0.7 Project and View functionality"
#VERSION="v0.8 Assigning Flosses"
#VERSION="v0.10 Start moving to pos for files"
#VERSION="v0.11 Project to save and load project flosses - NOT complete"
#VERSION="v0.12 Assign flosses manually"
#VERSION="v0.13 Seperate the View Flosses Window"
#VERSION="v0.14 Edit image - gamma and vibrancy"
#VERSION="v0.15 Project Save: Include project flosses"
#VERSION="v0.16 Reduce Size"
#VERSION="v0.17 Bug - reopening previously clossed windows"
#VERSION="v0.18 Legacy floss file export"
#VERSION="v0.19 Motif Editor"
#VERSION="v0.20 Motif Editor - integrate Motif Class"
#VERSION="v0.21 Motif Editor - Setup Motif Folder Structure on disk, save inidivdual motif"
#VERSION="v0.22 Motif Editor - load individual motif"
#VERSION="v0.23 Motif Editor - new motif"
#VERSION="v0.24 Add info pane. Cleanup in motif edditing"
#VERSION="V0.25 Motif Deprication QMouse.pos warning, Save Motif as Svg"
#VERSION="V0.26 Motif Fixed QGraphics Scene warning on mouse up"
#VERSION="V0.27 Motif picklist on main window"
#VERSION="V0.28 Generate Pattern: Buuton enabled control. 
#VERSION="V0.29 Create the tilesets. "
#VERSION="V0.30 Set tilesize to 10 and  Calculate the intensity of all tiles"
#VERSION="V0.31 create_motif_map() Now correctly populates tiles from the motif svgs
#VERSION="V0.32 Redo Generate to tile the area with the selected motifs"
#VERSION="V0.33 Bug - New project info not completing"
#VERSION="V0.34 Single Stitch saved to tile as width 1. Add Single/Double/Both option"
#VERSION="V0.35 Create additional tiles if required and managed the stich weight accouding to Stitch Weight option"
#VERSION="V0.36 Save motif now moves the motif to 0,0 for creating the tileset"
#VERSION="V0.37 Check that Single, Double and both work correctly"
#VERSION="V0.38 Colour or B/W output"
#VERSION="V0.39 Remove extra Choose Motif button, saveMotif error causing gap in pattern resolved"
#VERSION="V0.40 Project Save - Motif Choice; Stitch Choice"
#VERSION="V0.41 Current generated image should be the picture - not the pattern"
#VERSION="V0.42 Project Save - Colour / B&W, BUG: Stitch loading as single on project load, Update Generated timestamp"
#VERSION="V0.43 Count stitches"
#VERSION="V0.44 BUG BGR - RGB conversion in picture label"
#VERSION="V0.45 BUG: DeDup stitches"
#VERSION="V0.46 Single Page Pattern"
#VERSION="V0.47 Crop to image functionality"
#VERSION="V0.48 Feedback: Change all fonts to Verdana and pattern text to black"
#VERSION="V0.49 Change name to original image. Rest states on New or OpenProject()"
#VERSION="V0.50 BUG: refresh info pane after undo/redo. Bug Reduce Colours Stops working."
#VERSION="V0.51 Show size and colours in fields after changing"
#VERSION="V0.52 BUG: Don't treat Background as flosses"
#VERSION="V0.53 BUG: Don't not allow generate unless flosses assigned"
#VERSION="V0.54 BUG: B&W should use grescale amended image but write out using only one floss"
#VERSION="0.55 Create Pattern File folder structure. Consider that existing projects do not have the structure and diff between gen and gen full. Remove toggle weight in Motif editor\n"
#VERSION="0.56 Generate Form"
#VERSION="0.57 Need to call Full Page pattern generation and likely re-use the stitch_map\n"
#VERSION="0.58 save and load project stitch_map and picture"
#VERSION="0.59 Create full pattern split by floss"
#VERSION="0.60 On new project bring a copy of the source image into the project folder"
#VERSION="0.61 Bug: Assigned  Flosses reset on project update. Add Page sizes to Generate Pattern form\n"
#VERSION="0.62 Split Page Pattern Part 1"
#VERSION="0.63 Config basic settings\n"
#VERSION="0.64 Settings Page to update settings"
#VERSION="0.65 The pattern grid is now based off the config settings"
#VERSION="0.66 In the settings dialog choose pattern and picture file type"
#VERSION="0.67 Save picture according to file type"
#VERSION="0.68 Unified Stitch Data"
#VERSION="0.69 Implement Split Page Single Colour"
#VERSION="0.70 Bug - split page grid numbering needs to be offset"
#VERSION="0.71 Add file types to Gen Dialog and project data."
# _3 saves picture to SVG AND to requested optional type
# _4 save full page pattern to specified type
# _5 patterns saved as PNG don't include numbers
# _6 save full page pattern split colours to specified type
# _7 split_colour as a seperate method 
# _8 save split page pattern to specified type
# _9 save split page pattern split colours to specified type
#VERSION="Removed: 0.72 trace image"
#VERSION="Removed 0.72_1 trace tuning"
#VERSION="Removed 0.72_2 colour vectorisation"
#VERSION="0.73: Improve quality of image resize - not sure about it"
#VERSION="0.74: Assign Flosses not resetting after new project"
#VERSION="0.75: Bug: Split page patterrn - stitches showing in numbers"
#VERSION="0.76: Add printing colour to Assign Flosses "
#VERSION="0.77: Assign Flosses formatting improvements "
#VERSION="0.78: Use Print Colours in patterns"
#VERSION="0.78_1: AssignFlosses- save Bug fix" 
#VERSION="0.78_2: Use the colour preference to choose which colours to use when generating the patterns"
#VERSION="0.79: Create Pattern and Page Keys"
#VERSION="0.79_1 Dynamic key field width"
#VERSION="0.80: Auto create Print Colours"
#VERSION="0.81: For split page patterns:Show 3 stitches of adjoining pages"
#VERSION="0.81_1: expand size ang grid to account for 3 stitch overlap"
#VERSION="0.81_2: Highlight Overlap"
#VERSION="0.82 Bug: Assign specific floss showing pattern file as only that colour"
#VERSION="0.83 Monochrome: Just one stitch colour and density"
#VERSION="0.84: Configure Thickness and style of stitches in pattern"
#VERSION="0.84.1: Bug Centre line showing in page patterns"
#VERSION="0.84.2: Bug: single colour '/' in filename"
#VERSION="0.84.3: 0.84: Configure Thickness and style of stitches in picture and pattern"
VERSION="0.85: Small amendments:" 
VERSION="0.85_1: Grid numbers only need to show on 10s"
VERSION="0.85_2: Bug: Config not loading"
VERSION="0.85_3: Bug: Selecting background should set appropriate floss line to 'Fabric'"
VERSION="0.85_4: Bug: All flosses (inc white) not assigned as Fabric - should be treated as stitches"
VERSION="0.85_5: Remove option to hide images "
VERSION="0.85_6: Bug: View Flosses - window-close does not set visible status to false"
VERSION="0.85_7: When the MainWindow() is closed, close all other open windows"
"""
VERSION="0.86: Page numbers in filenames should be incremetal"
VERSION="0.86_1: Add motif names to filenames"
VERSION="0.87: Info Pane Errors"
VERSION="0.87_1: Show flosses and/or Motifs as red if they are 0 or None"
VERSION="0.88: Load PSD for inital image"
VERSION="0.89: Manual select palette and reduce image window"
VERSION="0.89_1:  Manual Select Button"
VERSION="0.89_2:  #Auto Assign Button"
VERSION="0.89_3:  right click onto locked floss clears it"
VERSION="0.89_5:  left click onto locked floss brings up a selection of the 10 perseptiolly nearest flosses"
VERSION="0.89_6 left Click add locked colour - right click removes it"
VERSION="0.89_7:  Updating the list of locked flosses causes the updated image to be refreshed."
VERSION="0.89_8:  better colour matching"
VERSION="0.89_10:  speed up CIEDE2000 using Numpy"
VERSION="0.89_12:  works great irrespective of size or number of colours"
VERSION="0.89_13:  implement save - update amended image, update flosses; update info pane"
VERSION="0.89_14:  Remove Reduce Colours Button from main window"
VERSION="0.89_15:  Add Flosses directly within the ColorPaletteWindow"
VERSION="0.89_16:  If flosses have already been assigned these should be added to the locked flosses when opening the ColorPaletteWindow"
#Maunal colour rectiion only available if project loaded"
# right click of locked floss clears it.
# left click on a loacked floss brings up a selections box showing the 10 perceptually closest flosses to the click floss and allow the user to select one to replace the current selected floss

# Clear Selection Button
# Reduce Colours Button
# Undo Reduce Colours Button
# Save Button


BUTTON_WIDTH=140
KNOWN_NEXT="\n"
KNOWN_NEXT="\n"
KNOWN_NEXT+="\n"
KNOWN_NEXT+="\n"

KNOWN_BUGS= "Stubborn Bug. Selecting a non-white background - white floss still not used in stitches\n"
KNOWN_BUGS= "DIFFICULT BUG: Undo not working after resize\n"
KNOWN_BUGS+="Choose background button not reset on New Project\n"
KNOWN_BUGS+="\n"
KNOWN_BUGS+="Brands - adding brand does not save to file\n"
KNOWN_BUGS+="New project - getting error on reducing colours around stitch type not set - until project save\n"
KNOWN_BUGS+="New project - after reduce colour - undo need to reset filed to current colours\n"
KNOWN_BUGS+="Available flosses - ensure the words can be seen on dark flosses like black\n"

KNOWN_UI="\n"

KNOWN_ENHANCEMENTS="Deleteing brands\n"
KNOWN_ENHANCEMENTS="Inverse flag - to inversse the generation match for black aida\n"
KNOWN_ENHANCEMENTS+="Main Window - Status whether flosses have been assigned\n"
KNOWN_ENHANCEMENTS+="Add a function to manage the number of image copies available for undo\n"
KNOWN_ENHANCEMENTS+="Unsaved project changes notification\n"
KNOWN_ENHANCEMENTS+="Tracker for Amended Image; Project Save; Floss Assignment\n"
KNOWN_ENHANCEMENTS+="Introduce tracker class. datetime for Floss assigned; project save, Amended image update\n"
KNOWN_ENHANCEMENTS+="Should I be saving entire clases in the project save? What about the project date time info etc\n"

""" Testing
    # T Config Floss: Does adding Floss work
    # T Config Floss: Does delieting floss work
    # T Config Floss: Does deleting Floss work?
    # What happens if the RGB of a floss is changed after it has been assigned to an image
    # What happens if a floss is removed after being assigned to an image
"""
@dataclass
class StitchData:
    start_point: QPointF
    end_point: QPointF
    floss_id: str
    stitch_type: str  # 'single' or 'double'
    weight: float
    tile_id: Optional[str] = None
    metadata: Dict = None  # Extensible dictionary for future attributes

    def copy(self):
        return StitchData(
            start_point=QPointF(self.start_point),
            end_point=QPointF(self.end_point),
            floss_id=self.floss_id,
            stitch_type=self.stitch_type,
            weight=self.weight,
            tile_id=self.tile_id,
            metadata=self.metadata.copy() if self.metadata else None
        )
    
class StitchCollection:
    def __init__(self):
        self._stitches: List[StitchData] = []
        
    def add_stitch(self, stitch: StitchData):
        self._stitches.append(stitch)
    
    def get_svg_lines(self) -> List[tuple]:
        return [(stitch.start_point, stitch.end_point) for stitch in self._stitches]
    
    def get_stitch_map(self) -> Dict:
        # Group stitches by tile_id or other criteria
        stitch_map = {}
        for stitch in self._stitches:
            key = stitch.tile_id or "default"
            if key not in stitch_map:
                stitch_map[key] = []
            stitch_map[key].append(stitch)
        return stitch_map
    
    def filter_by_floss(self, floss_id: str) -> List[StitchData]:
        return [stitch for stitch in self._stitches if stitch.floss_id == floss_id]
    
    def get_stitches_by_position(self):
        """Returns a dictionary of stitches keyed by position."""
        stitch_dict = {}
        for stitch in self._stitches:
            coord_key = tuple(sorted([
                (stitch.start_point.x(), stitch.start_point.y()),
                (stitch.end_point.x(), stitch.end_point.y())
            ]))
            stitch_dict[coord_key] = (stitch, stitch.floss_id)
        return stitch_dict

    def items(self):
        """Compatibility method for old code expecting dictionary-like behavior."""
        return self.get_stitches_by_position().items() 

class Tile:
    def __init__(self, x, y, layer_number, motif_name):
        self.x = x  # Grid position x
        self.y = y  # Grid position y
        self.layer_number = layer_number
        self.motif_name = motif_name
        self.stitches = []
        self.intensity = 0  # Will be calculated after stitches are added
        
    def add_stitch(self, stitch):
        """Add a stitch to this tile"""
        self.stitches.append(stitch)
        
    def calculate_intensity(self):
        """Calculate intensity based on stitch coverage"""
        total_pixels = 100  # 10x10 tile
        covered_pixels = 0
        # Create a blank 10x10 grid
        grid = np.zeros((10, 10))
        
        # Draw each stitch onto the grid
        for stitch in self.stitches:
            # Use coordinates directly since they're already relative to tile
            start_x = stitch.stitchStart.x()
            start_y = stitch.stitchStart.y()
            end_x = stitch.stitchEnd.x()
            end_y = stitch.stitchEnd.y()
            
            # Draw line on grid
            cv2.line(grid, 
                    (int(start_x), int(start_y)),
                    (int(end_x), int(end_y)), 
                    1, 
                    thickness=stitch.weight)
        
        # Count covered pixels
        covered_pixels = np.count_nonzero(grid)
        self.intensity = int(255 * (1 - covered_pixels / total_pixels))
        



    def display_tile(self):
        """Display the tile in a window using OpenCV"""
        # Create a white 100x100 image (scaling up 10x for visibility)
        display_size = 100
        scale = display_size // 10
        image = np.full((display_size, display_size, 3), 255, dtype=np.uint8)
        
        # Draw each stitch scaled up
        for stitch in self.stitches:
            # Scale coordinates up by 10
            start_x = int(stitch.stitchStart.x() * scale)
            start_y = int(stitch.stitchStart.y() * scale)
            end_x = int(stitch.stitchEnd.x() * scale)
            end_y = int(stitch.stitchEnd.y() * scale)
            
            # Draw line with scaled thickness
            cv2.line(
                image,
                (start_x, start_y),
                (end_x, end_y),
                (0, 0, 0),  # Black color
                thickness=stitch.weight * scale
            )
    
            # Add text showing coordinates and intensity
            text = f"({self.x}, {self.y})"
            cv2.putText(
                image,
                text,
                (5, 15),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (0, 0, 255),  # Red color
                1
            )
            
        # Show the image in a window
        window_name = f"{self.motif_name}, L: {self.layer_number}) S:{len(self.stitches)}"
        cv2.imshow(window_name, image)
        cv2.waitKey(0)  # Wait for key press
        cv2.destroyWindow(window_name)

    def print_tile(self):
        """Print tile details"""
        print(f"\nTile at ({self.x}, {self.y}):")
        print(f"Motif: {self.motif_name}, Layer: {self.layer_number}")
        print(f"Intensity: {self.intensity}")
        print("Stitches:")
        for stitch in self.stitches:
            print(f"  Start: ({stitch.stitchStart.x()}, {stitch.stitchStart.y()}  End: ({stitch.stitchEnd.x()}, {stitch.stitchEnd.y()})")
class TileMapViewer(QDialog):
    def __init__(self, motif_map, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Tile Map Viewer")
        self.setModal(True)
        
        # Create layout
        layout = QVBoxLayout(self)
        
        # Create scroll area for tiles
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        content_layout = QGridLayout(scroll_content)
        
        # Process each tileset position
        max_cols = 8  # Number of tiles per row
        current_row = 0
        current_col = 0
        
        for coord, tileset in motif_map.tilesets.items():
            if tileset.has_content():
                # Create container for this tileset
                tileset_widget = QWidget()
                tileset_layout = QVBoxLayout(tileset_widget)
                
                # Add coordinate label
                coord_label = QLabel(f"Coordinate: ({coord[0]}, {coord[1]})")
                tileset_layout.addWidget(coord_label)
                
                # Create scene and view for drawing tiles
                scene = QGraphicsScene()
                view = QGraphicsView(scene)
                view.setFixedSize(100, 100)
                view.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
                view.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
                
                # Set the scene rect to match tile size (10x10)
                scene.setSceneRect(0, 0, 10, 10)
                
                # Scale view to fit 10x10 grid into 100x100 view
                view.scale(10, 10)  # Scale up by 10 to fit view
                view.setRenderHint(QPainter.Antialiasing)  # Add antialiasing
                
                # Draw each tile in the tileset
                for tile in tileset.tiles:
                    # Draw each stitch in the tile
                    for stitch in tile.stitches:
                        line = QGraphicsLineItem(
                            stitch.stitchStart.x(),
                            stitch.stitchStart.y(),
                            stitch.stitchEnd.x(),
                            stitch.stitchEnd.y()
                        )
                        # Scale down pen width to match the scaled view
                        line.setPen(QPen(Qt.black, stitch.weight/10))
                        scene.addItem(line)
                    
                    # Add intensity label
                    intensity_label = QLabel(f"Intensity: {tile.intensity}")
                    tileset_layout.addWidget(intensity_label)
                
                tileset_layout.addWidget(view)
                
                # Add to grid
                content_layout.addWidget(tileset_widget, current_row, current_col)
                current_col += 1
                if current_col >= max_cols:
                    current_col = 0
                    current_row += 1
        
        scroll.setWidget(scroll_content)
        layout.addWidget(scroll)
        
        # Add close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)
        
        self.setMinimumSize(800, 600)
class StitchCounts:
    def __init__(self):
        self.floss_counts = {}  # Dict of {floss_name: {'single': count, 'double': count, 'color_name': str, 'rgb': tuple}}
        self.total_single = 0
        self.total_double = 0
    
    def reset(self):
        """Reset all counters"""
        self.floss_counts.clear()
        self.total_single = 0
        self.total_double = 0
    
    def add_stitch(self, color, is_double, project_flosses):
        """Add stitch count for a specific color"""
        closest_floss = self.find_closest_floss(color, project_flosses)
        if closest_floss:
            floss_key = f"{closest_floss.brand}:{closest_floss.name}"
            
            if floss_key not in self.floss_counts:
                self.floss_counts[floss_key] = {
                    'single': 0,
                    'double': 0,
                    'color_name': closest_floss.color_name,
                    'rgb': closest_floss.rgb
                }
            
            if is_double:
                self.floss_counts[floss_key]['double'] += 1
                self.total_double += 1
            else:
                self.floss_counts[floss_key]['single'] += 1
                self.total_single += 1

    def find_closest_floss(self, color, project_flosses):
        """Find the closest matching floss color"""
        min_distance = float('inf')
        closest_floss = None
        
        for floss in project_flosses:
            # Calculate color distance
            distance = sum((a - b) ** 2 for a, b in zip(color, floss.rgb)) ** 0.5
            if distance < min_distance:
                min_distance = distance
                closest_floss = floss
        
        return closest_floss

    def get_floss_total(self, floss_key):
        if floss_key in self.floss_counts:
            return (self.floss_counts[floss_key]['single'] + 
                   self.floss_counts[floss_key]['double'])
        return 0
    
    def get_grand_total(self):
        return self.total_single + self.total_double
    
    def print_stitch_counts(self):
        """Print detailed breakdown of stitch counts"""
        print("\nStitch Count Summary:")
        print("-" * 50)
        print(f"Total Stitches: {self.get_grand_total()}")
        print(f"Single Stitches: {self.total_single}")
        print(f"Double Stitches: {self.total_double}")
        print("\nBreakdown by Floss:")
        print("-" * 50)
        
        for floss_key, counts in self.floss_counts.items():
            print(f"\nFloss: {floss_key}")
            print(f"Color Name: {counts['color_name']}")
            print(f"Single Stitches: {counts['single']}")
            print(f"Double Stitches: {counts['double']}")
            print(f"Total Stitches: {counts['single'] + counts['double']}")
            print(f"RGB: {counts['rgb']}")

class TileSet:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.tiles = []  # List of Tile objects
        
        # Create and add blank tile with intensity 255 (completely white)
        blank_tile = Tile(x, y, 0, "blank")  # layer 0 for blank tile
        blank_tile.intensity = 255  # No stitches = maximum intensity
        self.tiles.append(blank_tile)
        
    def add_tile(self, tile):
        """Add a tile to this tileset"""
        self.tiles.append(tile)
        # Sort tiles by intensity for easier matching later
        self.tiles.sort(key=lambda t: t.intensity)
        
    def has_content(self):
        """Returns True if this tileset contains more than just the blank tile"""
        return len(self.tiles) > 1  # Changed from > 0 to > 1
        
    def get_tiles_by_intensity(self, target_intensity, tolerance=50):
        """Returns tiles sorted by how closely they match the target intensity.
        Always includes the blank tile as a fallback option.
        """
        matching_tiles = []
        
        # Add all tiles with their intensity difference
        for tile in self.tiles:
            intensity_diff = abs(tile.intensity - target_intensity)
            matching_tiles.append((intensity_diff, tile))
        
        # Sort by intensity difference and return all tiles
        return [tile for _, tile in sorted(matching_tiles, key=lambda x: x[0])]
    
    
    def print_tileset(self):
        print(f"\nTileset at ({self.x}, {self.y})")
        for tile in self.tiles:
            tile.print_tile()
class Stitch:
    def __init__(self, start, end, weight):
        self.stitchStart = start
        self.stitchEnd = end
        self.weight = weight
        
    def __str__(self):
        return f"Stitch from {self.stitchStart} to {self.stitchEnd} with weight {self.weight}"
    
class Layer:
    # Used in Motif Editing
    def __init__(self, number, stitches=None):
        self.number = number
        self.stitches = stitches if stitches else []
    
    def copy(self):
        return Layer(self.number + 1, [Stitch(s.stitchStart, s.stitchEnd, s.weight) for s in self.stitches])

class Motif:
    # Used in motif editing
    def __init__(self):
        self.name = "motif"
        self.sFilename=""
        self.layers = [Layer(1)] 
        self.current_layer_index = 0
        self.tilesWide = 0
        self.tilesHigh = 0
   
    def print_motif(self):
        print(f"Motif: {self.name}")
        print(f"Width: {self.tilesWide}")
        print(f"Height: {self.tilesHigh}")
        for layer in self.layers:
            print(f"Layer: {layer.number}")
            for stitch in layer.stitches:
                print(stitch)
        
    @property
    def current_layer(self):
        return self.layers[self.current_layer_index]
    
    def add_layer(self):
        new_layer = self.current_layer.copy()
        self.layers.insert(self.current_layer_index + 1, new_layer)
        self.current_layer_index += 1

    def new(self):
        self.layers = [Layer(1)]
        self.current_layer_index = 0
        
    def delete_layer(self):
        if len(self.layers) > 1:
            self.layers.pop(self.current_layer_index)
            if self.current_layer_index >= len(self.layers):
                self.current_layer_index -= 1
            return True
        return False
        
    def next_layer(self):
        if self.current_layer_index < len(self.layers) - 1:
            self.current_layer_index += 1
            return True
        return False
        
    def previous_layer(self):
        if self.current_layer_index > 0:
            self.current_layer_index -= 1
            return True
        return False

    """def save(self, filename):
        with open(filename, 'w') as f:
            f.write(f"Motif: {self.name}\n")
            for layer in self.layers:
                f.write(f"Layer: {layer.number}\n")
                for stitch in layer.stitches:
                    f.write(f"{stitch.stitchStart.x(), {stitch.stitchStart.y(), {stitch.stitchEnd.x(), {stitch.stitchEnd.y(), {stitch.weight}\n")"""

    def findExtentsAcrossAllLayers(self):
        self.grid_size=10
        minX = self.grid_size*40
        minY = self.grid_size*40
        maxX = 0
        maxY = 0
        for layer in self.layers:
            for stitch in layer.stitches:
                #print(stitch)
                minX = min(minX, stitch.stitchStart.x(), stitch.stitchEnd.x())
                minY = min(minY, stitch.stitchStart.y(), stitch.stitchEnd.y())
                maxX = max(maxX, stitch.stitchStart.x(), stitch.stitchEnd.x())
                maxY = max(maxY, stitch.stitchStart.y(), stitch.stitchEnd.y())
            #print("minX: %d, minY: %d, maxX: %d, maxY: %d" % (minX, minY, maxX, maxY))
            #print("sizeX: %d, sizeY: %d" % ((maxX/40) - (minX/40), (maxY/40) - (minY/40)))
        return minX, minY, maxX, maxY

    def save_motif(self, posMotifsFilePath):
        print("in Motif.save_motif()")
        dtMotifSaved = datetime.datetime.now()
        
        # Find motif boundary first
        minX = float('inf')
        minY = float('inf')
        maxX = float('-inf')
        maxY = float('-inf')
        
        # Find the actual boundaries
        for layer in self.layers:
            for stitch in layer.stitches:
                minX = min(minX, stitch.stitchStart.x()//4, stitch.stitchEnd.x()//4)
                minY = min(minY, stitch.stitchStart.y()//4, stitch.stitchEnd.y()//4)
                maxX = max(maxX, stitch.stitchStart.x()//4, stitch.stitchEnd.x()//4)
                maxY = max(maxY, stitch.stitchStart.y()//4, stitch.stitchEnd.y()//4)


        # Calculate size in tiles - remove the +1 
        self.tilesWide = int((maxX - minX) // 10)
        self.tilesHigh = int((maxY - minY) // 10)
        
        # If the motif exactly fills its tiles, no adjustment needed
        # If there's a remainder, we need one more tile
        if (maxX - minX) % 10 != 0:
            self.tilesWide += 1
        if (maxY - minY) % 10 != 0:
            self.tilesHigh += 1

        name = self.name + ".MTF"      
        posFilename = posMotifsFilePath / name
        self.sFilename = str(posFilename)

        # Save Motif File
        with open(self.sFilename, 'w') as f:
            f.write(f"Motif: {self.name}\n")
            f.write(f"Width: {self.tilesWide}\n")
            f.write(f"Height: {self.tilesHigh}\n")
            
            for layer in self.layers:
                f.write(f"Layer: {layer.number}\n")
                for stitch in layer.stitches:
                    # Translate coordinates relative to (0,0)
                    start_x = (stitch.stitchStart.x()//4 - minX)
                    start_y = (stitch.stitchStart.y()//4 - minY)
                    end_x = (stitch.stitchEnd.x()//4 - minX)
                    end_y = (stitch.stitchEnd.y()//4 - minY)
                    f.write(f"{start_x}, {start_y}, {end_x}, {end_y}, {stitch.weight}\n")

        # Create SVG file with translated coordinates
        svg_name = self.name + ".svg"
        svg_filename = posMotifsFilePath / svg_name
        
        with open(str(svg_filename), 'w') as f:
            width = maxX - minX
            height = maxY - minY
            f.write(f'<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n')
            f.write(f'<svg width="{width}" height="{height}" viewBox="0 0 {width} {height}" xmlns="http://www.w3.org/2000/svg">\n')
            
            for layer in self.layers:
                f.write(f'  <g id="layer{layer.number}">\n')
                for stitch in layer.stitches:
                    x1 = stitch.stitchStart.x()//4 - minX
                    y1 = stitch.stitchStart.y()//4 - minY
                    x2 = stitch.stitchEnd.x()//4 - minX
                    y2 = stitch.stitchEnd.y()//4 - minY
                    f.write(f'    <line x1="{x1}" y1="{y1}" x2="{x2}" y2="{y2}" '
                        f'stroke="black" stroke-width="{stitch.weight}"/>\n')
                f.write('  </g>\n')
            
            f.write('</svg>\n')

            #print("Motif.save_motif() Files saved: MTF: %s SVG: %s" % (self.sFilename, str(svg_filename)))

    def choose_and_load_motif(self, posMotifsFilePath):
        print("Test: In Motif.load_motif(): posMotifsFilePath: %s" % (str(posMotifsFilePath)))
        
        # Show file dialog
        filename, _ = QFileDialog.getOpenFileName(
            None,
            "Load Motif",
            str(posMotifsFilePath),
            "Motif Files (*.MTF)"
        )
        
        if filename:
            try:
                # Clear existing layers
                self.layers = []
                self.current_layer_index = 0
                
                # Read and parse file
                with open(filename, 'r') as f:
                    lines = f.readlines()
                    current_layer = None
                    for line in lines:
                        if line.startswith("Motif:"):
                            self.name = line.split(":")[1].strip()
                        elif line.startswith("Layer:"):
                            number = int(line.split(":")[1].strip())
                            current_layer = Layer(number)
                            self.layers.append(current_layer)
                        elif line.startswith("Width:"):
                            number = int(line.split(":")[1].strip())
                            self.tilesWide = number
                        elif line.startswith("Height:"):
                            number = int(line.split(":")[1].strip())
                            self.tilesHigh = number
                        else:
                            parts = line.split(",")
                            start = QPointF(float(parts[0]), float(parts[1]))
                            end = QPointF(float(parts[2]), float(parts[3]))
                            weight = int(parts[4])
                            current_layer.stitches.append(Stitch(start*4, end*4, weight))
                
                print(f"Successfully loaded motif: {filename}")
                
                ## Show the loaded motif in TileViewerDialog
                #viewer = TileViewerDialog(self, None)
                #viewer.exec()
                
                return True
                
            except Exception as e:
                print(f"Error loading motif: {str(e)}")
                return False
        
        return False

    def load_motif(self, file_path):
        #print("Test: In Motif.load_motif(): file_path: %s" % (str(file_path)))
        if file_path.exists():
            try:
                # Clear existing layers
                self.layers = []
                self.current_layer_index = 0
                
                # Read and parse file
                with open(file_path, 'r') as f:
                    lines = f.readlines()
                    current_layer = None
                    for line in lines:
                        if line.startswith("Motif:"):
                            self.name = line.split(":")[1].strip()
                        elif line.startswith("Layer:"):
                            number = int(line.split(":")[1].strip())
                            current_layer = Layer(number)
                            print(f"Loaded layer: {number}")    
                            self.layers.append(current_layer)
                        elif line.startswith("Width:"):
                            number = int(line.split(":")[1].strip())
                            self.tilesWide = number
                        elif line.startswith("Height:"):
                            number = int(line.split(":")[1].strip())
                            self.tilesHigh = number
                        else:
                            parts = line.split(",")
                            start = QPointF(float(parts[0]), float(parts[1]))
                            end = QPointF(float(parts[2]), float(parts[3]))
                            weight = int(parts[4])
                            current_layer.stitches.append(Stitch(start, end, weight))
                            #print(f"Loaded stitch: {start} to {end} with weight {weight}")
            
                # Show the loaded motif in TileViewerDialog
                #viewer = TileViewerDialog(self, None)
                #viewer.exec()
                return True
                
            except Exception as e:
                print(f"Error loading motif: {str(e)}")
                return False
        
        return False

class MotifMap:
    def __init__(self):
        self.width = 0
        self.height = 0
        self.tilesets = {}
        
    def get_tileset(self, x, y):
        key = (x, y)
        if key not in self.tilesets:
            self.tilesets[key] = TileSet(x, y)
        return self.tilesets[key]
    
class TileViewerDialog(QDialog):
    def __init__(self, motif, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"Tile Viewer - {motif.name}")
        self.setModal(True)
        
        print("In TileViewerDialog")
        # Create main layout
        layout = QVBoxLayout(self)
        
        # Create scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        content_layout = QVBoxLayout(scroll_content)
        
        # Find motif boundary first
        minX, minY, maxX, maxY = motif.findExtentsAcrossAllLayers()
        
        # Process each layer
        for layer_idx, layer in enumerate(motif.layers):
            # Create layer group
            layer_group = QGroupBox(f"Layer {layer.number}")
            layer_layout = QGridLayout()
            
            # Group stitches by tile
            tile_stitches = {}
            for stitch in layer.stitches:
                # Calculate relative coordinates from motif boundary
                rel_start_x = stitch.stitchStart.x() - minX
                rel_start_y = stitch.stitchStart.y() - minY
                
                # Calculate tile position relative to motif boundary
                tile_x = int(rel_start_x // 10)
                tile_y = int(rel_start_y // 10)
                pos = (tile_x, tile_y)
                
                if pos not in tile_stitches:
                    tile_stitches[pos] = []
                
                # Store stitch with adjusted coordinates relative to tile
                adjusted_stitch = Stitch(
                    QPointF(rel_start_x, rel_start_y),
                    QPointF(stitch.stitchEnd.x() - minX, stitch.stitchEnd.y() - minY),
                    stitch.weight
                )
                tile_stitches[pos].append(adjusted_stitch)

            # Create a preview for each tile
            row = 0
            col = 0
            max_cols = 5  # Number of tiles per row
            
            for (tile_x, tile_y), stitches in tile_stitches.items():
                # Create tile preview
                tile_widget = QWidget()
                tile_widget.setFixedSize(100, 100)
                tile_widget.setStyleSheet("background-color: white; border: 1px solid black;")
                
                # Create scene and view for tile
                scene = QGraphicsScene()
                view = QGraphicsView(scene)
                view.setFixedSize(100, 100)
                view.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
                view.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
                
                # Draw stitches
                for stitch in stitches:
                    # Convert coordinates to tile-local space
                    start_x = stitch.stitchStart.x() % 10 * 10
                    start_y = stitch.stitchStart.y() % 10 * 10
                    end_x = stitch.stitchEnd.x() % 10 * 10
                    end_y = stitch.stitchEnd.y() % 10 * 10
                    
                    line = QGraphicsLineItem(start_x, start_y, end_x, end_y)
                    line.setPen(QPen(Qt.black, stitch.weight))
                    scene.addItem(line)
                
                # Add position label
                label = QLabel(f"Tile ({tile_x}, {tile_y})")
                label.setAlignment(Qt.AlignCenter)
                
                # Add to layout
                tile_container = QWidget()
                tile_layout = QVBoxLayout(tile_container)
                tile_layout.addWidget(view)
                tile_layout.addWidget(label)
                
                layer_layout.addWidget(tile_container, row, col)
                
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
            
            layer_group.setLayout(layer_layout)
            content_layout.addWidget(layer_group)
        
        scroll.setWidget(scroll_content)
        layout.addWidget(scroll)
        
        # Add close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)
        
        self.setMinimumSize(800, 600)

class Project:
    def __init__(self):
        self.projectName=""
        self.posProjectDir="" # this is the (Path) projectDir
        self.posPatternDir=""
        self.posPatternFullDir=""
        self.posPatternFullSCDir=""
        self.posPatternPageDir=""
        self.posPatternPageSCDir=""
        self.sFilenameSrc="" # This is the full path and filename as it could be anywhere
        self.sFilenameAmended="" # This is the filename only as it is in the project dir
        #self.sFilenamePattern="" # This is the filename only as it is in the project dir
        self.sFilenamePicture="" # This is the filename only as it is in the project dir
        self.flosses=[]
        self.assigned_flosses = {}
        self.bUnsavedChanges=False
        self.stitch_type= ""
        self.dtProjectSaved=datetime.datetime.min  # Datetime the project was last saved
        self.dtAmendedUpdated=datetime.datetime.min  # Datetime the Amended Image was last changed
        self.dtFlossesAssigned=datetime.datetime.min  # Datetime the project flosses were changed
        self.dtGenerated=datetime.datetime.min  # Datetime the pattern was generated
        self.selected_motifs = []
        self.stitch_map = {}  # Key: tuple(start_x, start_y, end_x, end_y), Value: (stitch, floss_key)
        self.stichWeight=""
        self.use_colour=True
        #filenameFullPattern=""
        #filenameFullKey=""
        #filenamePDF=""
        #filenameSplitPatterns=[]
        self.stitch_counts = StitchCounts()
        self.background_colour = (255, 255, 255) # RGB Tuple
        self.full_grid_svg = None
        self.page_grid_svg = None
        self.pattern_file_format = None  # Options: "SVG", "PDF"
        self.picture_file_format = None  # Options: "SVG", "PDF", "PNG"
        self.print_colors = {}  # Dict to store print colors {floss_id: (print_color1, print_color2)}
        
    def printFlosses(self):
        for floss in self.flosses:
            print("Brand: %s Name: %s Colour: %s  RGB: %s" % (floss.brand, floss.name, floss.color_name, floss.rgb))

    def printColours(self):
        # print all the print colours
        for floss_id, print_colors in self.print_colors.items():
            print(f"Print Colors for Floss ID {floss_id}: {print_colors}")

    def updateAllFlosses(self, flosses):
        self.flosses = flosses
        
    def addFloss(self, floss):
        if floss not in self.flosses:
            self.flosses.append(floss)
        #    print("Project Floss Added. Brand: %s Name: %s Colour: %s" % (floss.brand, floss.name, floss.color_name))
        #else: 
        #    print("Project Floss Dupliace. Brand: %s Name: %s" % (floss.brand, floss.name))

    def new_project(self, projectName, posBaseDir):
        self.projectName=projectName
        self.assigned_flosses = {}  
        # Set default background color (white)
        self.background_colour = (255, 255, 255)  # RGB format
        project_dir = Path(posBaseDir) / projectName
        pattern_dir = Path(project_dir) / "Patterns"
        pattern_full_dir = Path(pattern_dir) / "Full"
        pattern_full_sc_dir = Path(pattern_full_dir) / "Single Colour"
        pattern_page_dir = Path(pattern_dir) / "Page"
        pattern_page_sc_dir = Path(pattern_page_dir) / "Single Colour"
        print("project_dir: %s, pattern_dir: %s" % (str(project_dir), str(pattern_dir)))
        project_dir.mkdir(parents=True, exist_ok=True)  
        pattern_dir.mkdir(parents=True, exist_ok=True)  
        pattern_full_dir.mkdir(parents=True, exist_ok=True)  
        pattern_full_sc_dir.mkdir(parents=True, exist_ok=True)  
        pattern_page_dir.mkdir(parents=True, exist_ok=True)  
        pattern_page_sc_dir.mkdir(parents=True, exist_ok=True)  
        
        self.posProjectDir=project_dir
        self.posPatternDir=pattern_dir
        self.posPatternFullDir=pattern_full_dir
        self.posPatternFullSCDir=pattern_full_sc_dir
        self.posPatternPageDir=pattern_page_dir
        self.posPatternPageSCDir=pattern_page_sc_dir
        
        # Set file formats from config
        self.pattern_file_format = config.pattern_file_format
        self.picture_file_format = config.picture_file_format

    def save_project(self, posProjectsFilePath, amended_image):
        print("in Project.save_project()")
        dtProjectSaved=datetime.datetime.now()
        print("dtLastSave, %s" % (str(dtProjectSaved)))

        name=self.projectName+".SMP"
        if (self.sFilenameAmended==""):
            posFullPath = Path(self.sFilenameSrc)
            #self.filenameAmended=full_path.stem + "_Amended" + full_path.suffix    
            self.sFilenameAmended = f"{posFullPath.stem}_Amended.png"
            print("posFullPath.stem: %s" % (str(posFullPath.stem)))
            print("posFullPath: %s" % (str(posFullPath)))
            print("self.sFilenameAmended: %s" % (str(self.sFilenameAmended)))

        posFilename=posProjectsFilePath / name
        print("posFilename: %s" % (str(posFilename)))

        # Save Project File
        print("sFilenmeSrc: %s" % self.sFilenameSrc)      
        project_data = {
            'projectName': self.projectName,
            'projectDir': self.posProjectDir,
            'patternDir': self.posPatternDir,
            'patternFullDir': self.posPatternFullDir,
            'patternFullSCDir': self.posPatternFullSCDir,
            'patternPageDir': self.posPatternPageDir,
            'patternPageSCDir': self.posPatternPageSCDir,
            'filenameSrc': self.sFilenameSrc,
            'filenameAmended': self.sFilenameAmended,
            'filenamePicture': self.sFilenamePicture,
            'projectSaved': dtProjectSaved,
            'amendedUpdated': self.dtAmendedUpdated,
            'flossesAssigned': self.dtFlossesAssigned,
            'assigned_flosses': self.assigned_flosses,
            'generated': self.dtGenerated,  # Add generation datetime
            'flosses': self.flosses,
            'selected_motifs': self.selected_motifs,  # Add selected motifs
            'stitch_type': self.stitch_type,  # Add stitch type
            'use_colour': self.use_colour,
            'stitch_counts': self.stitch_counts,
            'stitch_map': self.stitch_map,
            'background_colour': self.background_colour,
            'print_colors': self.print_colors,
            'pattern_file_format': self.pattern_file_format,
            'picture_file_format': self.picture_file_format            
        }
        with open(str(posFilename), 'wb') as file:
            pickle.dump(project_data, file)

        # Save Amended Image File
        print("B4 setting image_filepath")
        posImageFilepath = Path(self.posProjectDir) / self.sFilenameAmended
        print("posImageFilepath: %s " % (str(posImageFilepath))) 
                                      
        cv2.imwrite(str(posImageFilepath), amended_image)
        self.dtProjectSaved=dtProjectSaved
        print("after writing image")

    def open_project(self, posFilePath):
        print("In Project.open_project()")
        with open(posFilePath, 'rb') as file:
            loaded_data = pickle.load(file) 

            #project_data = json.load(file)
            self.projectName = loaded_data["projectName"]
            self.posProjectDir = loaded_data["projectDir"]
            self.posPatternDir=loaded_data["patternDir"]
            self.posPatternFullDir=loaded_data["patternFullDir"]
            self.posPatternFullSCDir=loaded_data["patternFullSCDir"]
            self.posPatternPageDir=loaded_data["patternPageDir"]
            self.posPatternPageSCDir=loaded_data["patternPageSCDir"]
            self.sFilenameSrc = loaded_data["filenameSrc"]
            self.sFilenameAmended = loaded_data["filenameAmended"]
            self.sFilenamePicture = loaded_data["filenamePicture"]
            self.dtProjectSaved = loaded_data["projectSaved"]
            self.dtAmendedUpdated = loaded_data["amendedUpdated"]
            self.flosses = loaded_data["flosses"]
            self.dtFlossesAssigned = loaded_data["flossesAssigned"]
            self.assigned_flosses = loaded_data["assigned_flosses"]
            # Add loading of new fields with defaults if not present
            #self.dtFlossesAssigned = loaded_data.get("flossesAssigned", datetime.datetime.min)
            self.selected_motifs = loaded_data.get("selected_motifs", [])
            self.stitch_type = loaded_data.get("stitch_type")
            self.posProjectDir=Path(self.posProjectDir)
            self.use_colour = loaded_data.get("use_colour")
            self.dtGenerated = loaded_data.get("generated", datetime.datetime.min)  # Add generation datetime
            self.stitch_counts = loaded_data.get('stitch_counts', StitchCounts())
            self.stitch_map = loaded_data.get('stitch_map', {})
            self.background_colour = loaded_data.get('background_colour', None)
            self.pattern_file_format = loaded_data.get('pattern_file_format', 'pdf')
            self.picture_file_format = loaded_data.get('picture_file_format', 'pdf')
            self.print_colors = loaded_data.get('print_colors', {})


    def get_info(self):
        title_txt = "Project Name: " + self.projectName + "\n"
        detail_txt = "Project Directory: " + str(self.posProjectDir) + "\n"
        detail_txt += "Source Image: " + self.sFilenameSrc + "\n"
        detail_txt += "Amended Image: " + self.sFilenameAmended + "\n"
        detail_txt += "# Flosses: " + str(len(self.flosses)) + "\n"

        # Add stitch count summary
        stitch_summary = f"Total Stitches: {self.stitch_counts.get_grand_total()}\n"
        stitch_summary += f"Single: {self.stitch_counts.total_single} Double: {self.stitch_counts.total_double}"

        # Create detailed tooltip
        tooltip = "Stitch Count Details:\n"
        tooltip += f"Total Stitches: {self.stitch_counts.get_grand_total()}\n"
        tooltip += f"Single Stitches: {self.stitch_counts.total_single}\n"
        tooltip += f"Double Stitches: {self.stitch_counts.total_double}\n\n"
        tooltip += "Breakdown by Floss:\n"

        for floss_key, counts in self.stitch_counts.floss_counts.items():
            tooltip += f"\n{floss_key}\n"
            tooltip += f"Color: {counts['color_name']}\n"
            tooltip += f"Single: {counts['single']} Double: {counts['double']}\n"
            tooltip += f"Total: {counts['single'] + counts['double']}\n"

        return title_txt, detail_txt, stitch_summary, tooltip

class SMConfig:
    def __init__(self):
        self.posHome_dir = self.set_home_dir()
        self.posProjects_dir  = self.set_projects_dir()
        self.posMotifs_dir = self.set_motifs_dir()
        self.maxLoadColours = 256
        self.kmeans_epsilon = 1 # reduce to say 0.1 for higher quality
        self.kmeans_max_iter = 10 # increase to say 100 for higher quality
        self.extension_project = ".SMP"

        self.single_stitch_line_thickness = 4
        self.single_stitch_Line_type = "solid"

        self.double_stitch_line_thickness = 4
        self.double_stitch_Line_type = "dashed"

        self.grid_minor_line_thickness = 1
        self.grid_minor_line_type = "dotted"
        self.grid_minor_colour = (1, 1, 1)  # Near Black

        self.grid_major_line_thickness = 1
        self.grid_major_line_type = "dotted"
        self.grid_major_colour = (7,177,194)  # Teal

        self.grid_centre_line_thickness = 2
        self.grid_centre_line_type = "dotted"
        self.grid_centre_colour = (194,24,7)  # Red

        # File format settings
        self.pattern_file_format = "SVG"  # Options: "SVG", "PDF"
        self.picture_file_format = "SVG"  # Options: "SVG", "PDF", "PNG"

        # Try to load settings from file
        self.load_settings()

    def load_settings(self):
        """Load settings from JSON file, create if doesn't exist"""
        config_path = Path(self.posHome_dir) / "settings.json"

        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    settings = json.load(f)
                    
                    # Load each setting, falling back to defaults if not present
                    self.single_stitch_line_thickness = settings.get('single_stitch_line_thickness', self.single_stitch_line_thickness)
                    self.single_stitch_Line_type = settings.get('single_stitch_Line_type', self.single_stitch_Line_type)
                    self.double_stitch_line_thickness = settings.get('double_stitch_line_thickness', self.double_stitch_line_thickness)
                    self.double_stitch_Line_type = settings.get('double_stitch_Line_type', self.double_stitch_Line_type)
                    self.grid_minor_line_thickness = settings.get('grid_minor_line_thickness', self.grid_minor_line_thickness)
                    self.grid_minor_line_type = settings.get('grid_minor_line_type', self.grid_minor_line_type)
                    self.grid_minor_colour = tuple(settings.get('grid_minor_colour', self.grid_minor_colour))
                    self.grid_major_line_thickness = settings.get('grid_major_line_thickness', self.grid_major_line_thickness)
                    self.grid_major_line_type = settings.get('grid_major_line_type', self.grid_major_line_type)
                    self.grid_major_colour = tuple(settings.get('grid_major_colour', self.grid_major_colour))
                    self.grid_centre_line_thickness = settings.get('grid_centre_line_thickness', self.grid_centre_line_thickness)
                    self.grid_centre_line_type = settings.get('grid_centre_line_type', self.grid_centre_line_type)
                    self.grid_centre_colour = tuple(settings.get('grid_centre_colour', self.grid_centre_colour))
                    self.pattern_file_format = settings.get('pattern_file_format', self.pattern_file_format)
                    self.picture_file_format = settings.get('picture_file_format', self.picture_file_format)
            except Exception as e:
                print(f"Error loading settings: {e}")
                # Create new settings file with defaults
                self.save_default_settings()
        else:
            # Create new settings file with defaults 
            self.save_default_settings()
                
    def save_default_settings(self):
        """Save current settings as defaults"""
        config_path = Path(self.posHome_dir) / "settings.json"
        settings = {
            'single_stitch_line_thickness': self.single_stitch_line_thickness,
            'single_stitch_Line_type': self.single_stitch_Line_type,
            'double_stitch_line_thickness': self.double_stitch_line_thickness,
            'double_stitch_Line_type': self.double_stitch_Line_type,
            'grid_minor_line_thickness': self.grid_minor_line_thickness,
            'grid_minor_line_type': self.grid_minor_line_type,
            'grid_minor_colour': self.grid_minor_colour,
            'grid_major_line_thickness': self.grid_major_line_thickness,
            'grid_major_line_type': self.grid_major_line_type,
            'grid_major_colour': self.grid_major_colour,
            'grid_centre_line_thickness': self.grid_centre_line_thickness,
            'grid_centre_line_type': self.grid_centre_line_type,
            'grid_centre_colour': self.grid_centre_colour,
            'pattern_file_format': self.pattern_file_format,
            'picture_file_format': self.picture_file_format
        }
        
        with open(config_path, 'w') as f:
            json.dump(settings, f, indent=4)

    def set_home_dir(self):
        posDir = Path.home() / "Stitch Master"
        posDir.mkdir(parents=True, exist_ok=True)
        return posDir

    def set_projects_dir(self):
        posDir = Path(self.posHome_dir) / "Projects"
        posDir.mkdir(parents=True, exist_ok=True)
        return posDir

    def set_motifs_dir(self):
        posDir = Path(self.posHome_dir) / "Motifs"
        posDir.mkdir(parents=True, exist_ok=True)
        return posDir
    
    def selectHomeDir(self):
        pass
                        
class SettingsDialog(QDialog):
    def __init__(self, config, parent=None):
        super().__init__(parent)
        self.config = config
        self.setWindowTitle("Settings")
        self.setMinimumWidth(400)
        
        # Create main layout
        layout = QVBoxLayout(self)
        
        # Create form layout for settings
        form = QFormLayout()
        
        # Single Stitch Settings
        self.single_thickness = QSpinBox()
        self.single_thickness.setRange(1, 6)
        self.single_thickness.setValue(self.config.single_stitch_line_thickness)
        form.addRow("Single Stitch Thickness:", self.single_thickness)
        
        self.single_type = QComboBox()
        self.single_type.addItems(["solid", "dashed", "dotted"])
        self.single_type.setCurrentText(self.config.single_stitch_Line_type)
        form.addRow("Single Stitch Line Type:", self.single_type)
        
        # Double Stitch Settings
        self.double_thickness = QSpinBox()
        self.double_thickness.setRange(1, 6)
        self.double_thickness.setValue(self.config.double_stitch_line_thickness)
        form.addRow("Double Stitch Thickness:", self.double_thickness)
        
        self.double_type = QComboBox()
        self.double_type.addItems(["solid", "dashed", "dotted"])
        self.double_type.setCurrentText(self.config.double_stitch_Line_type)
        form.addRow("Double Stitch Line Type:", self.double_type)
        
        # Grid Settings
        self.minor_thickness = QSpinBox()
        self.minor_thickness.setRange(1, 6)
        self.minor_thickness.setValue(self.config.grid_minor_line_thickness)
        form.addRow("Minor Grid Line Thickness:", self.minor_thickness)
        
        self.minor_type = QComboBox()
        self.minor_type.addItems(["solid", "dashed", "dotted"])
        self.minor_type.setCurrentText(self.config.grid_minor_line_type)
        form.addRow("Minor Grid Line Type:", self.minor_type)
        
        self.minor_color = QPushButton()
        self.minor_color.setStyleSheet(
            f"background-color: rgb({self.config.grid_minor_colour[0]}, "
            f"{self.config.grid_minor_colour[1]}, {self.config.grid_minor_colour[2]})")
        self.minor_color.clicked.connect(lambda: self.choose_color("minor"))
        form.addRow("Minor Grid Color:", self.minor_color)
        
        # Major Grid Settings
        self.major_thickness = QSpinBox()
        self.major_thickness.setRange(1, 6)
        self.major_thickness.setValue(self.config.grid_major_line_thickness)
        form.addRow("Major Grid Line Thickness:", self.major_thickness)
        
        self.major_type = QComboBox()
        self.major_type.addItems(["solid", "dashed", "dotted"])
        self.major_type.setCurrentText(self.config.grid_major_line_type)
        form.addRow("Major Grid Line Type:", self.major_type)
        
        self.major_color = QPushButton()
        self.major_color.setStyleSheet(
            f"background-color: rgb({self.config.grid_major_colour[0]}, "
            f"{self.config.grid_major_colour[1]}, {self.config.grid_major_colour[2]})")
        self.major_color.clicked.connect(lambda: self.choose_color("major"))
        form.addRow("Major Grid Color:", self.major_color)
        
        # Center Line Settings
        self.center_thickness = QSpinBox()
        self.center_thickness.setRange(1, 6)
        self.center_thickness.setValue(self.config.grid_centre_line_thickness)
        form.addRow("Center Line Thickness:", self.center_thickness)
        
        self.center_type = QComboBox()
        self.center_type.addItems(["solid", "dashed", "dotted"])
        self.center_type.setCurrentText(self.config.grid_centre_line_type)
        form.addRow("Center Line Type:", self.center_type)
        
        self.center_color = QPushButton()
        self.center_color.setStyleSheet(
            f"background-color: rgb({self.config.grid_centre_colour[0]}, "
            f"{self.config.grid_centre_colour[1]}, {self.config.grid_centre_colour[2]})")
        self.center_color.clicked.connect(lambda: self.choose_color("center"))
        form.addRow("Center Line Color:", self.center_color)
        
        layout.addLayout(form)
        
        # Create file format settings group
        format_group = QGroupBox("File Format Settings")
        format_layout = QFormLayout()
        
        # Pattern file format
        self.pattern_format = QComboBox()
        self.pattern_format.addItems(["SVG", "PDF"])
        self.pattern_format.setCurrentText(self.config.pattern_file_format)
        format_layout.addRow("Pattern File Format:", self.pattern_format)
        
        # Picture file format
        self.picture_format = QComboBox()
        self.picture_format.addItems(["SVG", "PDF", "PNG"])
        self.picture_format.setCurrentText(self.config.picture_file_format)
        format_layout.addRow("Picture File Format:", self.picture_format)
        
        format_group.setLayout(format_layout)
        layout.addWidget(format_group)
        
        # Add buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Save | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.save_settings)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def choose_color(self, line_type):
        color = QColorDialog.getColor()
        if color.isValid():
            button = getattr(self, f"{line_type}_color")
            button.setStyleSheet(f"background-color: {color.name()}")
            
    def save_settings(self):
        
        # Update config with new values
        self.config.single_stitch_line_thickness = self.single_thickness.value()
        self.config.single_stitch_Line_type = self.single_type.currentText()
        
        self.config.double_stitch_line_thickness = self.double_thickness.value()
        self.config.double_stitch_Line_type = self.double_type.currentText()
        
        self.config.grid_minor_line_thickness = self.minor_thickness.value()
        self.config.grid_minor_line_type = self.minor_type.currentText()
        color = self.minor_color.palette().button().color()
        self.config.grid_minor_colour = (color.red(), color.green(), color.blue())
        
        self.config.grid_major_line_thickness = self.major_thickness.value()
        self.config.grid_major_line_type = self.major_type.currentText()
        color = self.major_color.palette().button().color()
        self.config.grid_major_colour = (color.red(), color.green(), color.blue())
        
        self.config.grid_centre_line_thickness = self.center_thickness.value()
        self.config.grid_centre_line_type = self.center_type.currentText()
        color = self.center_color.palette().button().color()
        self.config.grid_centre_colour = (color.red(), color.green(), color.blue())
        
        # Save file format settings
        self.config.pattern_file_format = self.pattern_format.currentText()
        self.config.picture_file_format = self.picture_format.currentText()
        
        # Save to file using save_config_to_file()
        self.save_config_to_file()
        
        self.accept()
    
    def save_config_to_file(self):
        config_path = Path(self.config.posHome_dir) / "settings.json"
        settings = {
            'single_stitch_line_thickness': self.config.single_stitch_line_thickness,
            'single_stitch_Line_type': self.config.single_stitch_Line_type,
            'double_stitch_line_thickness': self.config.double_stitch_line_thickness,
            'double_stitch_Line_type': self.config.double_stitch_Line_type,
            'grid_minor_line_thickness': self.config.grid_minor_line_thickness,
            'grid_minor_line_type': self.config.grid_minor_line_type,
            'grid_minor_colour': self.config.grid_minor_colour,
            'grid_major_line_thickness': self.config.grid_major_line_thickness,
            'grid_major_line_type': self.config.grid_major_line_type,
            'grid_major_colour': self.config.grid_major_colour,
            'grid_centre_line_thickness': self.config.grid_centre_line_thickness,
            'grid_centre_line_type': self.config.grid_centre_line_type,
            'grid_centre_colour': self.config.grid_centre_colour,
            'pattern_file_format': self.config.pattern_file_format,
            'picture_file_format': self.config.picture_file_format,
        }
        
        with open(config_path, 'w') as f:
            json.dump(settings, f, indent=4)
class ImageProcessor:
    def __init__(self):
        self.gamma = 1.0
        self.image_loaded=False
        self.original_image = None
        self.amended_image = None
        self.picture_image = None
        self.floss_image = None
        self.history = []
        self.future = []
        self.original_image_colours=0
        self.amended_image_colours=0
        self.iAmendedMaxStitches=0
        self.iAmendedHeight=0
        self.iAmendedWidth=0
        self.iOriginalHeight=0
        self.iOriginalWidth=0
        self.config = None

    def get_motif_name_string(self, selected_motifs):
        """Create a string of motif names for filenames"""
        if not selected_motifs:
            return "NoMotif"
        
        try:
            # Handle both Motif objects and dictionaries
            motif_names = []
            for m in selected_motifs:
                if isinstance(m, dict):
                    # If it's a dictionary, get name from the dict
                    name = m.get('name', '')
                elif hasattr(m, 'name'):
                    # If it's a Motif object, get name directly
                    name = m.name
                else:
                    continue
                
                # Clean name for filename
                clean_name = name.replace(' ', '')
                motif_names.append(clean_name)
                
            return '_'.join(motif_names)
        except Exception as e:
            print(f"Error creating motif name string: {e}")
            return "ErrorMotif"

    def generate_key(self, project, stitch_collection, filepath, width, format):
        """Generate a key file showing floss details and skein calculations with dynamic column widths."""
        
        # Initialize counters and track max widths
        floss_data = {}
        max_widths = {
            'floss_key': 0,
            'color_name': 0,
            'stitches': 0,
            'skeins': 0
        }
        
        # Calculate totals for each floss and track maximum text widths
        for stitch in stitch_collection.get_stitches():
            floss_key = stitch.floss_id
            if floss_key not in floss_data:
                floss_data[floss_key] = {
                    'single': 0,
                    'double': 0,
                    'rgb_color': stitch.metadata['rgb_color'],
                    'color_name': project.stitch_counts.floss_counts[floss_key]['color_name']
                }
                # Update max widths
                max_widths['floss_key'] = max(max_widths['floss_key'], len(floss_key) * 7)
                max_widths['color_name'] = max(max_widths['color_name'], 
                                            len(floss_data[floss_key]['color_name']) * 7)
            
            if stitch.stitch_type == 'single':
                floss_data[floss_key]['single'] += 1
            else:
                floss_data[floss_key]['double'] += 1

        # Calculate maximum widths for numeric columns
        for data in floss_data.values():
            total_stitches = data['single'] + data['double']
            skeins = ((data['single'] + (2 * data['double'])) / 1450) / 6
            max_widths['stitches'] = max(max_widths['stitches'], len(str(total_stitches)) * 7)
            max_widths['skeins'] = max(max_widths['skeins'], len(f"{skeins:.2f}") * 7)

        # Add padding and minimum widths
        padding = 20
        color_box_width = 40
        min_column_width = 60
        
        # Ensure minimum widths
        for key in max_widths:
            max_widths[key] = max(max_widths[key], min_column_width)

        # Calculate total width and column positions
        total_width = (padding * 2 + color_box_width + 
                    sum(max_widths.values()) + 
                    (5 * 20))  # 20px spacing between columns

        # Create SVG content with dynamic widths
        row_height = 30
        header_height = 40
        num_rows = len(floss_data)
        table_height = header_height + (row_height * num_rows)
        
        svg_content = self.create_svg_header(total_width, table_height + (2 * padding))

        # Add table background and border
        svg_content += f'''
            <rect x="{padding}" y="{padding}" 
                width="{total_width-2*padding}" height="{table_height}"
                fill="white" stroke="black" stroke-width="1"/>
            
            <!-- Header row background -->
            <rect x="{padding}" y="{padding}" 
                width="{total_width-2*padding}" height="{header_height}"
                fill="#f0f0f0" stroke="black" stroke-width="1"/>
        '''

        # Calculate column x positions
        x_pos = {
            'color': padding + 10,
            'floss': padding + color_box_width + 30,
            'name': padding + color_box_width + max_widths['floss_key'] + 50,
            'stitches': padding + color_box_width + max_widths['floss_key'] + 
                    max_widths['color_name'] + 70,
            'skeins': padding + color_box_width + max_widths['floss_key'] + 
                    max_widths['color_name'] + max_widths['stitches'] + 90
        }

        # Add headers
        svg_content += f'''
            <text x="{x_pos['color']}" y="{padding + 25}" font-family="Arial" font-size="14" font-weight="bold">Colour</text>
            <text x="{x_pos['floss']}" y="{padding + 25}" font-family="Arial" font-size="14" font-weight="bold">Floss</text>
            <text x="{x_pos['name']}" y="{padding + 25}" font-family="Arial" font-size="14" font-weight="bold">Name</text>
            <text x="{x_pos['stitches']}" y="{padding + 25}" font-family="Arial" font-size="14" font-weight="bold">Stitches</text>
            <text x="{x_pos['skeins']}" y="{padding + 25}" font-family="Arial" font-size="14" font-weight="bold">Skeins</text>
        '''

        # Add vertical dividers
        for x in [x_pos['floss'] - 10, x_pos['name'] - 10, 
                x_pos['stitches'] - 10, x_pos['skeins'] - 10]:
            svg_content += f'''
                <line x1="{x}" y1="{padding}" x2="{x}" y2="{padding + table_height}" 
                    stroke="black" stroke-width="1"/>
            '''

        # Add rows for each floss
        y_pos = padding + header_height
        for floss_key, data in floss_data.items():
            total_threads = data['single'] + (2 * data['double'])
            skeins = total_threads / (1450 * 6)
            total_stitches = data['single'] + data['double']
            rgb = data['rgb_color']
            
            # Add row border
            svg_content += f'''
                <line x1="{padding}" y1="{y_pos}" x2="{total_width-padding}" y2="{y_pos}" 
                    stroke="black" stroke-width="1"/>
                <rect x="{x_pos['color']}" y="{y_pos + 5}" width="40" height="20" 
                    fill="rgb({rgb[0]},{rgb[1]},{rgb[2]})" stroke="black"/>
                <text x="{x_pos['floss']}" y="{y_pos + 20}" font-family="Arial" font-size="12">{floss_key}</text>
                <text x="{x_pos['name']}" y="{y_pos + 20}" font-family="Arial" font-size="12">{data['color_name']}</text>
                <text x="{x_pos['stitches']}" y="{y_pos + 20}" font-family="Arial" font-size="12">{total_stitches}</text>
                <text x="{x_pos['skeins']}" y="{y_pos + 20}" font-family="Arial" font-size="12">{skeins:.2f}</text>
            '''
            y_pos += row_height

        svg_content += '</g></svg>'
        
        # Save in requested format
        key_filename = filepath.stem + '_key' + filepath.suffix
        key_filepath = filepath.parent / key_filename
        
        self.save_key_file(key_filepath, svg_content, format, total_width, table_height + (2 * padding))
        
        return key_filepath

    def save_key_file(self, filepath, svg_content, format, width, height):
        """Save the key file in the specified format."""
        if format.upper() == "SVG":
            with open(filepath, 'w') as f:
                f.write(svg_content)
        elif format.upper() == "PDF":
            # Convert to PDF using temporary SVG
            temp_svg_path = filepath.with_suffix('.tmp.svg')
            with open(temp_svg_path, 'w') as f:
                f.write(svg_content)
            try:
                drawing = svg2rlg(str(temp_svg_path))
                renderPDF.drawToFile(drawing, str(filepath))
            finally:
                if temp_svg_path.exists():
                    temp_svg_path.unlink()
        else:  # PNG
            # Convert to PNG using temporary SVG
            temp_svg_path = filepath.with_suffix('.tmp.svg')
            with open(temp_svg_path, 'w') as f:
                f.write(svg_content)
            try:
                svg2png(url=str(temp_svg_path), write_to=str(filepath))
            finally:
                if temp_svg_path.exists():
                    temp_svg_path.unlink()

    def generate_pattern(self, project, config, selected_motifs, stitch_type="Single", pattern_color_mode='floss'):
        """Creates patterns using selected motifs with configurable color modes."""
        self.config = config

        if not selected_motifs:
            return
        print("Generating pattern... Mode:", pattern_color_mode)
        # Reset stitch counts
        project.stitch_counts.reset()
        
        # Initialize separate stitch collections for picture and pattern
        pattern_stitch_collection = StitchCollection()
        picture_stitch_collection = StitchCollection()
        project.stitch_map = {}

        # Initialize pattern image
        pattern_width = self.iAmendedWidth * 10
        pattern_height = self.iAmendedHeight * 10
        self.picture_image = np.full((pattern_height, pattern_width, 3), 255, dtype=np.uint8)
        
        # Create motif map with stitch type
        motif_map = create_motif_map(selected_motifs, stitch_type)

        # Convert background color from RGB to BGR for OpenCV comparison
        bg_color_bgr = (project.background_colour[2], 
                        project.background_colour[1], 
                        project.background_colour[0])

        def get_color_for_floss(floss_key, floss, mode):
            """Helper function to get the appropriate color based on mode"""
            if mode == 'floss':
                return floss.rgb
            elif mode == 'print1' and floss.name in project.print_colors:
                print1_color = project.print_colors[floss.name][0]
                if print1_color:
                    try:
                        return tuple(map(int, print1_color.split(',')))
                    except ValueError:
                        pass
            elif mode == 'print2' and floss.name in project.print_colors:
                print2_color = project.print_colors[floss.name][1]
                if print2_color:
                    try:
                        return tuple(map(int, print2_color.split(',')))
                    except ValueError:
                        pass
            return floss.rgb  # Fallback to floss color

        # Process all stitches
        for y in range(self.iAmendedHeight):
            for x in range(self.iAmendedWidth):
                pixel_color = self.amended_image[y, x]

                # Convert pixel color from BGR to RGB for floss matching
                rgb_color = (int(pixel_color[2]), int(pixel_color[1]), int(pixel_color[0]))
                
                # Find closest floss before background check
                closest_floss = project.stitch_counts.find_closest_floss(rgb_color, project.flosses)

                if np.allclose(pixel_color, bg_color_bgr, rtol=1e-05, atol=1):
                    continue

                # Only process non-fabric pixels
                if closest_floss and closest_floss.name != "Fabric":
                    pixel_intensity = self.get_pixel_intensity(x, y)
                    motif_x = x % motif_map.width if motif_map.width > 0 else 0
                    motif_y = y % motif_map.height if motif_map.height > 0 else 0
                    tileset = motif_map.get_tileset(motif_x, motif_y)

                    if tileset and tileset.has_content():
                        matching_tiles = tileset.get_tiles_by_intensity(pixel_intensity)
                        if matching_tiles:
                            best_tile = matching_tiles[0]
                            base_x = x * 10
                            base_y = y * 10

                            for old_stitch in best_tile.stitches:
                                # Process stitch coordinates
                                start_x = base_x + old_stitch.stitchStart.x()
                                start_y = base_y + old_stitch.stitchStart.y()
                                end_x = base_x + old_stitch.stitchEnd.x()
                                end_y = base_y + old_stitch.stitchEnd.y()

                                # Handle color information
                                if pattern_color_mode == 'monochrome':
                                    bgr_color = [0, 0, 0]
                                    rgb_color = (0, 0, 0)
                                else:
                                    bgr_color = pixel_color
                                    rgb_color = (int(bgr_color[2]), int(bgr_color[1]), int(bgr_color[0]))
                                    

                                # Find closest floss
                                floss_key = None
                                if project.flosses:
                                    closest_floss = project.stitch_counts.find_closest_floss(rgb_color, project.flosses)
                                    if closest_floss:
                                        floss_key = f"{closest_floss.brand}:{closest_floss.name}"

                                        # Get picture color (always floss RGB)
                                        picture_rgb = closest_floss.rgb
                                        picture_bgr = (picture_rgb[2], picture_rgb[1], picture_rgb[0])

                                        # Get pattern color based on mode
                                        pattern_rgb = get_color_for_floss(floss_key, closest_floss, pattern_color_mode)
                                        pattern_bgr = (pattern_rgb[2], pattern_rgb[1], pattern_rgb[0])

                                        new_picture_stitch = StitchData(
                                            start_point=QPointF(start_x, start_y),
                                            end_point=QPointF(end_x, end_y),
                                            floss_id=floss_key,
                                            stitch_type="double" if old_stitch.weight > 1 else "single",
                                            weight=old_stitch.weight,
                                            tile_id=f"tile_{x}_{y}",
                                            metadata={
                                                'rgb_color': picture_rgb,
                                                "intensity": pixel_intensity,
                                                "position": (x, y)
                                            }
                                        )

                                        new_pattern_stitch = StitchData(
                                            start_point=QPointF(start_x, start_y),
                                            end_point=QPointF(end_x, end_y),
                                            floss_id=floss_key,
                                            stitch_type="double" if old_stitch.weight > 1 else "single",
                                            weight=old_stitch.weight,
                                            tile_id=f"tile_{x}_{y}",
                                            metadata={
                                                'rgb_color': pattern_rgb,
                                                "intensity": pixel_intensity,
                                                "position": (x, y)
                                            }
                                        )


                                        # Check for duplicates
                                        stitch_key = (start_x, start_y, end_x, end_y)
                                        if stitch_key not in project.stitch_map:
                                            pattern_stitch_collection.add_stitch(new_pattern_stitch)
                                            picture_stitch_collection.add_stitch(new_picture_stitch)
                                            project.stitch_map[stitch_key] = (new_picture_stitch, floss_key)
                                            
                                            # Draw stitch on picture image using picture_rgb
                                            picture_bgr = (picture_rgb[2], picture_rgb[1], picture_rgb[0])
                                            cv2.line(
                                                self.picture_image,
                                                (int(start_x), int(start_y)),
                                                (int(end_x), int(end_y)),
                                                picture_bgr,
                                                thickness=new_picture_stitch.weight
                                            )

                                            # Update stitch counts
                                            count_type = 'double' if new_picture_stitch.weight > 1 else 'single'
                                            if floss_key not in project.stitch_counts.floss_counts:
                                                project.stitch_counts.floss_counts[floss_key] = {
                                                    'single': 0, 'double': 0,
                                                    'rgb': picture_rgb,
                                                    'color_name': closest_floss.color_name
                                                }

                                            project.stitch_counts.floss_counts[floss_key][count_type] += 1
                                            if count_type == 'double':
                                                project.stitch_counts.total_double += 1
                                            else:
                                                project.stitch_counts.total_single += 1


        # Save the picture file according to config
        picname = project.projectName + "Pic"


        #print("project.sFilenamePicture: %s" % (project.sFilenamePicture))
        if not project.sFilenamePicture:
            project.sFilenamePicture = f"{picname}.{self.config.picture_file_format.lower()}"


        posPictureFilepath = Path(project.posProjectDir) / project.sFilenamePicture


        self.save_as_image_file(
            "PICTURE",
            project.picture_file_format,
            posPictureFilepath, 
            picture_stitch_collection,
            "",
            pattern_width,
            pattern_height,
            project
        )

        # open the saved image file for consistency
        try:
            # Convert SVG to PNG
            tmp_png = str(posPictureFilepath.with_suffix('.png'))
            svg2png(url=str(posPictureFilepath), write_to=tmp_png)
            
            # Read PNG as OpenCV image
            self.picture_image = cv2.imread(tmp_png)
            
        finally:
            # Clean up temp files
            os.unlink(tmp_png)        

        project.dtGenerated = datetime.datetime.now()
        return self.picture_image, pattern_stitch_collection

    def generate_split_page_pattern(self, project, config, stitches_svg, pages_width, pages_height, split_colours=False):
        """Creates multiple page pattern with grid and stitches split into pages."""
        #print("generate_split_page_pattern")
        width = self.iAmendedWidth
        height = self.iAmendedHeight
        padding = 50  # Padding for numbering
        overlap_stitches = 2  # Number of stitches to show from adjacent pages
        
        # Calculate total number of pages
        total_pages = pages_width * pages_height
        current_page = 0

        overlapR=overlapB=0

        # Get motif names string
        motif_string = self.get_motif_name_string(project.selected_motifs)

        # Calculate dimensions for each page (in stitch units)
        stitches_per_page_width = math.ceil(width / pages_width)
        stitches_per_page_height = math.ceil(height / pages_height)
        #print("stitches_per_page_width: %s  stitches_per_page_height: %s" % (stitches_per_page_width, stitches_per_page_height))

        # How many pages wide and high is the pattern
        pagesWide=math.ceil(width / stitches_per_page_width)
        pagesHigh=math.ceil(height / stitches_per_page_height)
        #print("pagesWide: %s  pagesHigh: %s" % (pagesWide, pagesHigh))

        # Generate each page
        for page_y in range(pages_height):
            for page_x in range(pages_width):
                current_page += 1  # Increment page counter
                #print("page_x: %s  page_y: %s" % (page_x, page_y))
                # Determine whether the page has potential overlaps
                # Left overlap - all page that are not on the far left have a left overlap
                        
                # # Right overlap - all page that are not on the far right have a right overlap
                if page_x < pages_width - 1: overlapR = overlap_stitches
                else: overlapR = 0
                        
                # Bottom overlap - all page that are not on the bottom have a bottom overlap
                if page_y < pages_height - 1: overlapB = overlap_stitches
                else: overlapB = 0


                # Calculate page boundaries in stitch coordinates - taking into account overlaps
                start_x = page_x * stitches_per_page_width
                start_y = page_y * stitches_per_page_height
                end_x = (min((page_x + 1) * stitches_per_page_width, width))+overlapR
                end_y = (min((page_y + 1) * stitches_per_page_height, height))+overlapB
                #print("start_x: %s  start_y: %s  end_x: %s  end_y: %s" % (start_x, start_y, end_x, end_y))

                # Create a new StitchCollection for this page
                page_stitch_collection = StitchCollection()
                #overlap_stitch_collection = StitchCollection()  # For overlap stitches

                # Add stitches that fall within this page's boundaries
                for stitch in project.stitch_collection.get_stitches():
                    # Convert stitch coordinates back to stitch units
                    stitch_sx = math.floor(stitch.start_point.x() / 10)
                    stitch_sy = math.floor(stitch.start_point.y() / 10)
                    stitch_ex = math.floor(stitch.end_point.x() / 10)
                    stitch_ey = math.floor(stitch.end_point.y() / 10)

                    # Check if stitch belongs to current page
                    if ((start_x <= stitch_sx <= end_x and start_y <= stitch_sy <= end_y) and 
                        (start_x <= stitch_ex <= end_x and start_y <= stitch_ey <= end_y)):
                        new_stitch = stitch.copy()
                        # Adjust coordinates relative to page origin
                        new_x = (stitch_sx - start_x) * 10
                        new_y = (stitch_sy - start_y) * 10
                        
                        dx = stitch.end_point.x() - stitch.start_point.x()
                        dy = stitch.end_point.y() - stitch.start_point.y()
                        
                        new_stitch.start_point.setX(new_x)
                        new_stitch.start_point.setY(new_y)
                        new_stitch.end_point.setX(new_x + dx)
                        new_stitch.end_point.setY(new_y + dy)
                        
                        page_stitch_collection.add_stitch(new_stitch)
                    
                # Create page file
                page_width = (end_x - start_x) * 10
                page_height = (end_y - start_y) * 10
                #print("page_width: %s  page_height: %s" % (page_width, page_height))

                # Create grid with numbers
                project.page_grid_svg = self.create_pattern_grid(
                    config,
                    page_width,
                    page_height,
                    padding,
                    start_x,
                    start_y,
                    overlapR,
                    overlapB
                )

                # Create filename and save
                #page_filename = f"{project.projectName}_Page_{page_y+1}_{page_x+1}.svg"
                #page_filepath = Path(project.posPatternPageDir) / page_filename
                # Create filename with sequential numbering
                page_filename = f"{project.projectName}_{motif_string}_Page_{current_page}of{total_pages}.svg"
                page_filepath = Path(project.posPatternPageDir) / page_filename
                
                # Save main pattern with overlapping stitches
                self.save_as_image_file(
                    "PATTERN",
                    project.pattern_file_format,
                    page_filepath,
                    page_stitch_collection,
                    project.page_grid_svg,
                    page_width + (2 * padding),
                    page_height + (2 * padding),
                    project
                )

                if split_colours:
                    self.generate_split_colour_pattern(
                        project,
                        page_stitch_collection,
                        project.posPatternPageSCDir,
                        page_width + (2 * padding),
                        page_height + (2 * padding),
                        current_page,  # Pass current page number instead of x,y coords
                        total_pages,
                        motif_string
                    )

    def create_overlap_stitch(self, stitch, page_start_x, page_start_y):
        """Create a copy of stitch with overlap styling."""
        overlap_stitch = stitch.copy()
        
        # Get original stitch coordinates in stitch units
        orig_sx = math.floor(stitch.start_point.x() / 10)
        orig_sy = math.floor(stitch.start_point.y() / 10)
        orig_ex = math.floor(stitch.end_point.x() / 10)
        orig_ey = math.floor(stitch.end_point.y() / 10)
        
        # Adjust coordinates relative to page origin
        new_x = (orig_sx - page_start_x) * 10
        new_y = (orig_sy - page_start_y) * 10
        
        # Maintain the exact relative positions of endpoints
        dx = stitch.end_point.x() - stitch.start_point.x()
        dy = stitch.end_point.y() - stitch.start_point.y()
        
        overlap_stitch.start_point.setX(new_x)
        overlap_stitch.start_point.setY(new_y)
        overlap_stitch.end_point.setX(new_x + dx)
        overlap_stitch.end_point.setY(new_y + dy)
        
        # Add overlap metadata
        overlap_stitch.metadata['is_overlap'] = True
        overlap_stitch.metadata['overlap_color'] = (200, 230, 255)  # Pale blue
        
        return overlap_stitch

    def save_as_image_file(self, pic_or_pat, format, filepath: Path, stitch_collection, grid_svg_lines, width, height, project):
        """Save pattern with main stitches and overlap stitches to file."""
        if pic_or_pat=="PICTURE":
            print("pic_or_pat: %s  format: %s" % (pic_or_pat, format))
            print("filepath: %s   width: %s   height: %s" % (str(filepath), str(width), str(height)))
            # Always save a copy of the picture as SVG
            self.save_collection_as_image_file(
                "SVG", 
                filepath, 
                stitch_collection, 
                grid_svg_lines, 
                width, 
                height,
                0)
            if format.upper() != "SVG":
                self.save_collection_as_image_file(
                    format, 
                    filepath, 
                    stitch_collection, 
                    grid_svg_lines, 
                    width, 
                    height,
                    0)
        elif pic_or_pat=="PATTERN":
            padding=50
            self.save_collection_as_image_file(
                format, 
                filepath, 
                stitch_collection, 
                grid_svg_lines, 
                width+(2*padding), 
                height+(2*padding), 
                padding)

            # Generate corresponding key file
            self.generate_key(
                project,
                stitch_collection, 
                filepath,
                width+(2*padding),
                format)
        
    def save_collection_as_image_file(self, format, filepath: Path, stitch_collection, grid_svg_lines, width, height, padding):    
        """Save pattern with main stitches and overlap stitches to file."""
        print("SCAIF() file: %s   format: %s" % ((str(filepath)),format))
        print("SCAIF() width: %s   height: %s" % (str(width), str(height)))
        
        # Create SVG content
        svg_data = self.create_svg_header(width, height)
        
        # Add grid lines
        svg_data += grid_svg_lines
        
        # Add main stitches
        svg_data += self.stitch_collection_to_svg_lines(stitch_collection, padding)

        # Save in requested format
        if format.upper() == "SVG":
            with open(filepath, 'w') as f:
                f.write(svg_data)
        elif format.upper() == "PDF":
            # Handle PDF conversion
            temp_svg_path = filepath.with_suffix('.tmp.svg')
            with open(temp_svg_path, 'w') as f:
                f.write(svg_data)
            try:
                drawing = svg2rlg(str(temp_svg_path))
                renderPDF.drawToFile(drawing, str(filepath))
            finally:
                if temp_svg_path.exists():
                    temp_svg_path.unlink()
        else:  # PNG
            # Handle PNG conversion
            temp_svg_path = filepath.with_suffix('.tmp.svg')
            with open(temp_svg_path, 'w') as f:
                f.write(svg_data)
            try:
                svg2png(url=str(temp_svg_path), write_to=str(filepath))
            finally:
                if temp_svg_path.exists():
                    temp_svg_path.unlink()

    def generate_single_pattern(self, project, config, stitches_svg, split_colours=False):
        """Creates a single page pattern with grid and stitches."""
        width = self.iAmendedWidth *10
        height = self.iAmendedHeight *10
        padding = 50  # Padding for numbering
        print("generate_single_pattern()")
        print("width: %s   height: %s" % (str(width), str(height)))
        motif_string = self.get_motif_name_string(project.selected_motifs)

        
        # Create grid
        project.full_grid_svg = self.create_pattern_grid(config,width, height, padding, 0, 0)
        
        # Save to file
        pattern_filename = f"{project.projectName}_{motif_string}_Single.svg"
        pattern_filepath = Path(project.posPatternFullDir) / pattern_filename
        
        print("pattern_filepath: %s" % (str(pattern_filepath)))
        
        self.save_as_image_file(
            "PATTERN",
            project.pattern_file_format,
            pattern_filepath, 
            project.stitch_collection, 
            project.full_grid_svg, 
            width, 
            height,
            project)

        if split_colours:
            self.generate_split_colour_pattern(project, project.stitch_collection, 
                                               project.posPatternFullSCDir, width, height, 0, 0, motif_string)

    def generate_split_colour_pattern(self, project, stitch_collection, filepath_in, width, height, 
                                      current_page=0, total_pages=0, motif_string=None):
        """Creates separate pattern files for each floss color using the existing grid."""
        print("generate_single_pattern_split_colours()")
        print("width: %s   height: %s" % (str(width), str(height)))
        padding = 50  # Same padding as used in create_pattern_grid
        
        # Group stitches by floss
        floss_stitches = {}
        for stitch in stitch_collection.get_stitches():
            floss_key = stitch.floss_id
            if floss_key not in floss_stitches:
                floss_stitches[floss_key] = []
            floss_stitches[floss_key].append(stitch)
        
        # Generate a separate file for each floss color
        for floss_key, floss_specific_stitches in floss_stitches.items():
            if not floss_key:  # Skip if no floss key (might be None or empty)
                continue
                
            # Create a new StitchCollection for this color
            color_stitch_collection = StitchCollection()
            for stitch in floss_specific_stitches:
                color_stitch_collection.add_stitch(stitch)
            
            # Create filename for this floss
            # Strip ": " from the floss key
            #tmp_floss_key = floss_key
            tmp_floss_key = floss_key.replace(":", "")
            #tmp_floss_key = tmp_floss_key.replace("DMC/", "DMC")

            # Create filename based on whether it's a split page or single page
            if current_page > 0 and total_pages > 0:
                filename = f"{project.projectName}_{motif_string}_{tmp_floss_key}_Page_{current_page}of{total_pages}.svg"
                grid_svg=project.page_grid_svg
            else:
                filename = f"{project.projectName}_{motif_string}_Single_{tmp_floss_key}.svg"
                grid_svg=project.full_grid_svg

            #if (pageX==0) and (pageY == 0):
            #    print("Should be single page: pageX: %s   pageY: %s" % (str(pageX), str(pageY)))
            #    filename = f"{project.projectName}_Single_{tmp_floss_key}.svg"
            #    grid_svg=project.full_grid_svg
            #else:
            #    print("Should be multi page: pageX: %s   pageY: %s" % (str(pageX), str(pageY)))
            #    filename = f"{project.projectName}_{tmp_floss_key}_{pageX}_{pageY}.svg"
            #    grid_svg=project.page_grid_svg
            filepath = Path(filepath_in) / filename
            
            # Save using save_as_image_file
            print("filepath: %s" % (str(filepath)))
            self.save_as_image_file(
                "PATTERN",
                project.pattern_file_format,
                filepath,
                color_stitch_collection,
                grid_svg,
                width,
                height,
                project
            )        

    def old_generate_split_page_pattern(self, project, stitches_svg, pages_width, pages_height, split_colours=False):
        """Creates multiple page pattern with grid and stitches split into pages."""
        print("generate_split_page_pattern")
        width = self.iAmendedWidth
        height = self.iAmendedHeight
        padding = 50  # Padding for numbering
        
        # Calculate dimensions for each page (in stitch units)
        stitches_per_page_width = math.ceil(width / pages_width)
        stitches_per_page_height = math.ceil(height / pages_height)
        print(f"Pages wide: {pages_width}, Pages high: {pages_height}")
        print(f"stitches_per_page_width: {stitches_per_page_width}, stitches_per_page_height: {stitches_per_page_height}")

        # Generate each page
        for page_y in range(pages_height):
            for page_x in range(pages_width):
                # Calculate page boundaries in stitch coordinates 
                start_x = page_x * stitches_per_page_width
                start_y = page_y * stitches_per_page_height
                end_x = min((page_x + 1) * stitches_per_page_width, width)
                end_y = min((page_y + 1) * stitches_per_page_height, height)
                print(f"Page {page_y+1}_{page_x+1}: ({start_x}, {start_y}) to ({end_x}, {end_y})")

                # Create a new StitchCollection for this page
                page_stitch_collection = StitchCollection()

                # Add stitches that fall within this page's boundaries
                for stitch in project.stitch_collection.get_stitches():
                    # Convert stitch coordinates back to stitch units
                    stitch_sx = math.floor(stitch.start_point.x() / 10)  # Use floor to ensure proper boundary alignment
                    stitch_sy = math.floor(stitch.start_point.y() / 10)
                    stitch_ex = math.floor(stitch.end_point.x() / 10)
                    stitch_ey = math.floor(stitch.end_point.y() / 10)

                    #print("if (start_x: %s <= stitch_sx: %s <= end_x: %s) and (start_y: %s <= stitch_sy: %s <= end_y: %s)" % (str(start_x), str(stitch_sx), str(end_x), str(start_y), str(stitch_sy), str(end_y)))
                    #print(" anf (start_x: %s <= stitch_ex: %s <= end_x: %s) and (start_y: %s <= stitch_ey: %s <= end_y: %s)" % (str(start_x), str(stitch_ex), str(end_x), str(start_y), str(stitch_ey), str(end_y)))
                    #print("stitch_sx: %s   stitch_sy: %s" % (str(stitch_sx), str(stitch_sy)))
                    #print("end_x: %s   end_y: %s" % (str(stitch.end_point.x()), str(stitch.end_point.y())))
                    #print("stitch_ex: %s   stitch_ey: %s" % (str(stitch_ex), str(stitch_ey)))
                    if ((start_x <= stitch_sx <= end_x and start_y <= stitch_sy <= end_y)) and ((start_x <= stitch_ex <= end_x and start_y <= stitch_ey <= end_y)):
                        # Create a copy of the stitch with adjusted coordinates
                        new_stitch = stitch.copy()
                        #print("Kept: sx: %s   sy: %s   ex: %s   ey: %s" % (str(stitch_sx), str(stitch_sy), str(stitch_ex), str(stitch_ey)))
                        #print("stitch_x: %s   stitch_y: %s" % (str(stitch_sx), str(stitch_sy)))
                        #print("stitch.start_point.x(): %s   stitch.start_point.y(): %s" % (str(stitch.start_point.x()), str(stitch.start_point.y())))
                        # Adjust coordinates relative to page origin (in pixel units)
                        new_x = (stitch_sx - start_x) * 10 
                        new_y = (stitch_sy - start_y) * 10
                        #print("new_x: %s   new_y: %s" % (str(new_x), str(new_y)))

                        # Maintain the exact relative positions of start and end points
                        dx = stitch.end_point.x() - stitch.start_point.x()
                        dy = stitch.end_point.y() - stitch.start_point.y()
                        #print("dx: %s   dy: %s" % (str(dx), str(dy)))

                        new_stitch.start_point.setX(new_x)
                        new_stitch.start_point.setY(new_y)
                        new_stitch.end_point.setX(new_x + dx)
                        new_stitch.end_point.setY(new_y + dy)
                        #print("new_stitch.start_point.x(): %s   new_stitch.start_point.y(): %s" % (str(new_stitch.start_point.x()), str(new_stitch.start_point.y())))
                        #print("new_stitch.end_point.x(): %s   new_stitch.end_point.y(): %s" % (str(new_stitch.end_point.x()), str(new_stitch.end_point.y())))
                        page_stitch_collection.add_stitch(new_stitch)

                # Calculate number offsets for continuous numbering
                number_offset_x = page_x * stitches_per_page_width
                number_offset_y = page_y * stitches_per_page_height

                # Create grid for this page
                page_width = (end_x - start_x) * 10
                page_height = (end_y - start_y) * 10
                project.page_grid_svg = self.create_pattern_grid(
                    page_width,
                    page_height,
                    padding,
                    number_offset_x,
                    number_offset_y
                )

                # Create filename for this page
                page_filename = f"{project.projectName}_Page_{page_y+1}_{page_x+1}.svg"
                page_filepath = Path(project.posPatternPageDir) / page_filename

                # Save using save_as_image_file
                self.save_as_image_file(
                    "PATTERN",
                    project.pattern_file_format,
                    page_filepath,
                    page_stitch_collection,
                    project.page_grid_svg,
                    page_width + (2 * padding),
                    page_height + (2 * padding),
                    project
                )

                if split_colours:
                    self.generate_split_colour_pattern(
                        project,
                        page_stitch_collection,
                        project.posPatternPageSCDir,
                        page_width + (2 * padding),
                        page_height + (2 * padding),
                        page_x+1,
                        page_y+1
                    )

    def _extract_stitch_coordinates(self, stitch_line):
        #Helper method to extract coordinates from SVG line element.
        import re
        coords = re.findall(r'x1="(\d+\.?\d*)" y1="(\d+\.?\d*)" x2="(\d+\.?\d*)" y2="(\d+\.?\d*)"', stitch_line)
        if coords:
            return [float(x) for x in coords[0]]
        return None

    def create_svg_header(self, width: int, height: int) -> str:
        """Creates standard SVG header as a string."""
        return (f'<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n'
                f'<svg width="{width}" height="{height}" '
                f'viewBox="0 0 {width} {height}" '
                f'xmlns="http://www.w3.org/2000/svg">\n'
                f'<rect width="100%" height="100%" fill="white"/>\n'
                f'<g id="pattern">\n')

    def save_svglines_as_pdf(self, filepath: Path, svg_content: str, width: int, height: int):
            """Save SVG content as PDF file"""
            from reportlab.pdfgen import canvas
            from reportlab.lib.units import mm
            from reportlab.lib.colors import Color
            import re

            # Convert pixel dimensions to mm (assuming 1 pixel = 0.264583 mm)
            px_to_mm = 0.264583
            pdf_width = width * px_to_mm
            pdf_height = height * px_to_mm
            
            c = canvas.Canvas(str(filepath), pagesize=(pdf_width*mm, pdf_height*mm))
            c.scale(px_to_mm, px_to_mm)  # Scale to convert pixels to mm
            
            def parse_color(color_str):
                """Parse SVG color string to RGB values"""
                if color_str.startswith('rgb'):
                    # Parse rgb(r,g,b) format
                    r, g, b = map(int, re.findall(r'\d+', color_str))
                    return (r/255, g/255, b/255)
                elif color_str.startswith('#'):
                    # Parse hex color
                    color_str = color_str.lstrip('#')
                    r = int(color_str[0:2], 16)
                    g = int(color_str[2:4], 16)
                    b = int(color_str[4:6], 16)
                    return (r/255, g/255, b/255)
                return (0, 0, 0)  # Default to black

            # Parse SVG content using regular expressions
            line_pattern = re.compile(r'<line[^>]*?x1="([^"]*)"[^>]*?y1="([^"]*)"[^>]*?x2="([^"]*)"[^>]*?y2="([^"]*)"[^>]*?stroke="([^"]*)"[^>]*?stroke-width="([^"]*)"')
            
            for match in line_pattern.finditer(svg_content):
                x1, y1, x2, y2, stroke, stroke_width = match.groups()
                rgb = parse_color(stroke)
                c.setStrokeColor(Color(*rgb))
                c.setLineWidth(float(stroke_width))
                c.line(float(x1), float(y1), float(x2), float(y2))
            
            c.save()

    def stitch_collection_to_svg_lines(self, stitch_collection: StitchCollection, padding) -> str:
        """Converts a StitchCollection to SVG line elements as a string."""
        svg_lines = []
        
        for stitch in stitch_collection.get_stitches():
            rgb_color = stitch.metadata['rgb_color']
            svg_color = f"rgb({rgb_color[0]},{rgb_color[1]},{rgb_color[2]})"
            
            # Add padding to all coordinates
            x1 = stitch.start_point.x() + padding
            y1 = stitch.start_point.y() + padding
            x2 = stitch.end_point.x() + padding
            y2 = stitch.end_point.y() + padding


            # Get stitch style based on type
            if stitch.stitch_type == 'single':
                thickness = self.config.single_stitch_line_thickness
                line_type = self.config.single_stitch_Line_type
            else:  # double
                thickness = self.config.double_stitch_line_thickness
                line_type = self.config.double_stitch_Line_type

            # Add dash pattern if needed
            dash_pattern = ''
            if line_type == 'dashed':
                dash_pattern = 'stroke-dasharray="4,4"'
            elif line_type == 'dotted':
                dash_pattern = 'stroke-dasharray="2,2"'

            line = (f'  <line x1="{x1}" '
                    f'y1="{y1}" '
                    f'x2="{x2}" '
                    f'y2="{y2}" '
                    f'stroke="{svg_color}" '
                    f'stroke-width="{thickness}" '
                    f'{dash_pattern}/>\n')
            
            svg_lines.append(line)

        return ''.join(svg_lines) + '</g>\n</svg>'

    def create_pattern_grid(self, config, width, height, padding=50, start_stitch_x=0, start_stitch_y=0, overlapRight=0, overlapBottom=0):
        """Creates an SVG grid with numbers, adjusting for overlap stitches."""
        # Calculate base dimensions
        stitches_wide = int(width/10)
        stitches_high = int(height/10)
        
        # Calculate center coordinates of full pattern (in stitch units)
        full_pattern_center_x = (self.iAmendedWidth // 2)
        full_pattern_center_y = (self.iAmendedHeight // 2)

        # Check if this page contains either center line
        has_vertical_center = (start_stitch_x <= full_pattern_center_x <= start_stitch_x + stitches_wide)
        has_horizontal_center = (start_stitch_y <= full_pattern_center_y <= start_stitch_y + stitches_high)
        
        # Determine overlap extensions
        overlap_right = overlapRight 
        overlap_bottom = overlapBottom

        # Adjust grid dimensions for overlaps
        adjusted_width = width
        adjusted_height = height
        adjusted_svg_width = adjusted_width + (2 * padding)
        adjusted_svg_height = adjusted_height + (2 * padding)

        # Start grid content
        svg = '  <g id="grid">\n'

        # Add overlap highlight rectangles if needed
        if overlap_right > 0:
            svg += f'''    <rect x="{padding + width - (overlap_right * 10)}" y="{padding}"
                        width="{overlap_right * 10}" height="{height}" 
                        fill="rgb(180,200,255)" fill-opacity="0.5" stroke="rgb(100,150,255)" stroke-width="2"/>\n'''

        if overlap_bottom > 0:
            svg += f'''    <rect x="{padding}" y="{padding + height - (overlap_bottom * 10)}" width="{width}"
                        height="{overlap_bottom * 10}" fill="rgb(180,200,255)" fill-opacity="0.5" 
                        stroke="rgb(100,150,255)" stroke-width="2"/>\n'''

        # Helper function to get stroke-dasharray for line types
        def get_stroke_pattern(line_type):
            if line_type == "dashed":
                return 'stroke-dasharray="4,4"'
            elif line_type == "dotted":
                return 'stroke-dasharray="2,2"'
            return ''  # solid line

        # Minor grid lines
        svg += f'  <g id="minor_grid" stroke="rgb({config.grid_minor_colour[0]},{config.grid_minor_colour[1]},{config.grid_minor_colour[2]})" stroke-width="{config.grid_minor_line_thickness}">\n'
        minor_pattern = get_stroke_pattern(config.grid_minor_line_type)
        
        for x in range(stitches_wide + 1):
            if x % 10 != 0:  # Only minor lines
                x_pos = padding + (x * 10)
                svg += f'    <line x1="{x_pos}" y1="{padding}" x2="{x_pos}" y2="{adjusted_svg_height-padding}" {minor_pattern}/>\n'
        
        for y in range(stitches_high + 1):
            if y % 10 != 0:  # Only minor lines
                y_pos = padding + (y * 10)
                svg += f'    <line x1="{padding}" y1="{y_pos}" x2="{adjusted_svg_width-padding}" y2="{y_pos}" {minor_pattern}/>\n'
        svg += '  </g>\n'

        # Major grid lines (every 10 cells)
        svg += f'  <g id="major_grid" stroke="rgb({config.grid_major_colour[0]},{config.grid_major_colour[1]},{config.grid_major_colour[2]})" stroke-width="{config.grid_major_line_thickness}">\n'
        major_pattern = get_stroke_pattern(config.grid_major_line_type)
        
        for x in range(0, stitches_wide + 1, 10):
            x_pos = padding + (x * 10)
            svg += f'    <line x1="{x_pos}" y1="{padding}" x2="{x_pos}" y2="{adjusted_svg_height-padding}" {major_pattern}/>\n'
        
        for y in range(0, stitches_high + 1, 10):
            y_pos = padding + (y * 10)
            svg += f'    <line x1="{padding}" y1="{y_pos}" x2="{adjusted_svg_width-padding}" y2="{y_pos}" {major_pattern}/>\n'
        svg += '  </g>\n'

        # Center lines - only if this page contains the pattern center
        if has_vertical_center or has_horizontal_center:
            svg += f'  <g id="center_lines" stroke="rgb({config.grid_centre_colour[0]},{config.grid_centre_colour[1]},{config.grid_centre_colour[2]})" stroke-width="{config.grid_centre_line_thickness}">\n'
            center_pattern = get_stroke_pattern(config.grid_centre_line_type)
            
            if has_vertical_center:
                # Calculate x position relative to page start
                center_x = padding + ((full_pattern_center_x - start_stitch_x) * 10)
                svg += f'    <line x1="{center_x}" y1="{padding}" x2="{center_x}" y2="{adjusted_svg_height-padding}" {center_pattern}/>\n'
            
            if has_horizontal_center:
                # Calculate y position relative to page start
                center_y = padding + ((full_pattern_center_y - start_stitch_y) * 10)
                svg += f'    <line x1="{padding}" y1="{center_y}" x2="{adjusted_svg_width-padding}" y2="{center_y}" {center_pattern}/>\n'
            
            svg += '  </g>\n'

        # Add numbers with correct offsets
        svg += '  <g id="numbers" font-family="Arial" font-size="8" fill="black">\n'
        
        # Horizontal numbers
        for x in range(0, stitches_wide + 1, 10):
            number = x + start_stitch_x
            x_pos = padding + (x * 10)
            # Top numbers
            svg += f'    <text x="{x_pos}" y="{padding-2}" text-anchor="middle">{number}</text>\n'
            # Bottom numbers
            svg += f'    <text x="{x_pos}" y="{adjusted_svg_height-padding+10}" text-anchor="middle">{number}</text>\n'
        
        # Vertical numbers
        for y in range(0, stitches_high + 1, 10):
            number = y + start_stitch_y
            y_pos = padding + (y * 10) + 2
            # Left numbers
            svg += f'    <text x="{padding-2}" y="{y_pos}" text-anchor="end">{number}</text>\n'
            # Right numbers
            svg += f'    <text x="{adjusted_svg_width-padding+10}" y="{y_pos}" text-anchor="end">{number}</text>\n'
        
        svg += '  </g></g>\n'
        return svg

    def set_config(self, config):
        """Set the config object for the image processor"""
        self.config = config

    def openImage(self, posImagePath):
        """Open and load an image file, including PSD format support."""
        print("In ImageProcessor.openImage()")
        
        # Get file extension
        file_ext = posImagePath.lower().split('.')[-1]
        
        if file_ext == 'psd':
            # Load PSD file
            try:
                psd = PSDImage.open(posImagePath)
                # Convert PSD to PIL Image
                # Use composite_image() instead of compose()
                pil_image = psd.composite()
                if pil_image is None:
                    print("Failed to compose PSD image")
                    return False
                    
                # Convert PIL to OpenCV format (RGB to BGR)
                self.original_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            except Exception as e:
                print(f"Error loading PSD file: {e}")
                return False
        else:
            # Load other image formats directly with OpenCV
            self.original_image = cv2.imread(posImagePath)

        if self.original_image is None:
            print("Failed to load image")
            return False

        self.amended_image = self.original_image.copy()
        self.history = [self.original_image.copy()]
        self.future = []
        
        print("Original image shape:", self.original_image.shape)
        print("Amended image shape:", self.amended_image.shape)
        
        self.image_loaded = True
        self.original_image_colours = self.get_number_of_colors(self.original_image)
        self.amended_image_colours = self.original_image_colours
        print(f"Orig # Colours: {self.original_image_colours}   Amended # Colours: {self.amended_image_colours}")
        
        self.iOriginalHeight, self.iOriginalWidth = self.original_image.shape[:2]         
        self.iAmendedHeight, self.iAmendedWidth = self.amended_image.shape[:2] 
        self.iAmendedMaxStitches = max(self.iAmendedWidth, self.iAmendedHeight)
        
        return True

    def old_openImage(self,posImagePath):
        print("In ImageProcessor.openImage()")
        self.original_image = cv2.imread(posImagePath)
        self.amended_image = self.original_image.copy()
        self.history = [self.original_image.copy()]
        self.future = []
        print("Original image shape: ", self.original_image.shape)
        print("Amended image shape: ", self.amended_image.shape)
        self.image_loaded=True
        self.original_image_colours=self.get_number_of_colors(self.original_image)
        self.amended_image_colours=self.original_image_colours
        print("Orig # Colours: %d   Amended # Colours: %d" % (self.original_image_colours, self.amended_image_colours))
        self.iOriginalHeight, self.iOriginalWidth = self.original_image.shape[:2]         
        self.iAmendedHeight, self.iAmendedWidth = self.amended_image.shape[:2] 
        # Determine the maximum value between width and height 
        self.iAmendedMaxStitches = max(self.iAmendedWidth, self.iAmendedHeight)

    def openImages(self, sSrcFilename, posAmendedImagePath, posPictureImagePath):
        """Open source, amended and picture files, with PSD support for source file."""
        print("In ImageProcessor.openImages() src: %s  amended: %s  picture: %s" % (sSrcFilename, str(posAmendedImagePath), str(posPictureImagePath)))
        
        # Handle source file which could be PSD
        file_ext = Path(sSrcFilename).suffix.lower()
        if file_ext == '.psd':
            try:
                psd = PSDImage.open(sSrcFilename)
                # Convert PSD to PIL Image
                pil_image = psd.composite()
                if pil_image is None:
                    print("Failed to compose PSD image")
                    return False
                    
                # Convert PIL to OpenCV format (RGB to BGR)
                self.original_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            except Exception as e:
                print(f"Error loading PSD source file: {e}")
                return False
        else:
            # Load other image formats directly with OpenCV
            self.original_image = cv2.imread(sSrcFilename)

        # Load amended image (always standard format)
        self.amended_image = cv2.imread(str(posAmendedImagePath))
        
        # Load picture if provided
        if posPictureImagePath is not None:
            self.picture_image = open_svg(str(posPictureImagePath))
                
        self.history = [self.amended_image.copy()]
        self.future = []
        
        self.image_loaded = True
        self.original_image_colours = self.get_number_of_colors(self.original_image)
        self.amended_image_colours = self.get_number_of_colors(self.amended_image)
        
        self.iOriginalHeight, self.iOriginalWidth = self.original_image.shape[:2]         
        self.iAmendedHeight, self.iAmendedWidth = self.amended_image.shape[:2] 
        # Determine the maximum value between width and height 
        self.iAmendedMaxStitches = max(self.iAmendedWidth, self.iAmendedHeight)
        
        return True

    def old_openImages(self,sSrcFilename, posAmendedImagePath, posPictureImagePath):
        print("In ImageProcessor.openImages() src: %s  amended: %s  picture: %s" % (sSrcFilename, str(posAmendedImagePath), str(posPictureImagePath)))
        self.original_image = cv2.imread(sSrcFilename)
        self.amended_image = cv2.imread(str(posAmendedImagePath))
        #self.picture_image = cv2.imread(str(posPictureImagePath))
        if posPictureImagePath != None:
            self.picture_image=open_svg(str(posPictureImagePath))
            
        self.history = [self.amended_image.copy()]
        self.future = []
        #print("Original image shape: ", self.original_image.shape)
        #print("Amended image shape: ", self.amended_image.shape)
        self.image_loaded=True
        self.original_image_colours=self.get_number_of_colors(self.original_image)
        self.amended_image_colours=self.get_number_of_colors(self.amended_image)
        
        self.iOriginalHeight, self.iOriginalWidth = self.original_image.shape[:2]         
        self.iAmendedHeight, self.iAmendedWidth = self.amended_image.shape[:2] 
        # Determine the maximum value between width and height 
        self.iAmendedMaxStitches = max(self.iAmendedWidth, self.iAmendedHeight)

    def adjust_gamma(self, image, gamma):
        print("In adjust_gamma()")
        if self.image_loaded == True:
            inv_gamma = 1.0 / gamma
            table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
            return cv2.LUT(image, table)

    def process_image(self, image):
        print("In ImageProcessor.process_image()")
        image = self.adjust_gamma(image, self.gamma)
        return self.reduce_colors2(image, self.num_colors)

    def reduce_colors(self, num_colors):
        print("In ImageProcessor.reduce_colors(). Reduce to %d colours" % (num_colors))
        
        # Add safety check for very small number of colors
        if num_colors < 1:
            print("Invalid number of colors")
            return
            
        # Add timeout parameter to kmeans
        lab_image = cv2.cvtColor(self.amended_image, cv2.COLOR_BGR2LAB)
        pixels = lab_image.reshape((-1, 3))
        
        # Set specific termination criteria with timeout
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 0.1)
        
        # Add flags parameter and attempts
        try:
            _, labels, palette = cv2.kmeans(
                pixels.astype(np.float32), 
                num_colors, 
                None,
                criteria, 
                attempts=10,
                flags=cv2.KMEANS_PP_CENTERS
            )
            
            # Process results
            quantized = palette[labels.flatten()]
            quantized = quantized.reshape(lab_image.shape)
            self.amended_image = cv2.cvtColor(quantized.astype(np.uint8), cv2.COLOR_LAB2BGR)
            
            # Update history and color count
            self.history.append(self.amended_image.copy())
            self.future.clear()
            self.amended_image_colours = self.get_number_of_colors(self.amended_image)
            
            print(f"Successfully reduced to {self.amended_image_colours} colors")
            return True
            
        except Exception as e:
            print(f"Error in color reduction: {e}")
            return False

    def vec_change_size(self, size):
        """
        High-quality image resizing that preserves exact colors.
        """
        try:
            # Store current image in history
            self.history.append(self.amended_image.copy())
            
            # Get dimensions and calculate scaling
            height, width = self.amended_image.shape[:2]
            if width > height:
                scale_factor = size / width
            else:
                scale_factor = size / height

            # Calculate new dimensions
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)

            # Get unique colors from original image
            unique_colors = np.unique(self.amended_image.reshape(-1, self.amended_image.shape[2]), axis=0)
            color_map = {tuple(color): color for color in unique_colors}

            # Perform initial resize
            resized_image = cv2.resize(
                self.amended_image,
                (new_width, new_height),
                interpolation=cv2.INTER_NEAREST  # Use nearest neighbor to avoid color blending
            )

            # For each pixel in resized image, map to closest original color
            resized_pixels = resized_image.reshape(-1, 3)
            for i, pixel in enumerate(resized_pixels):
                min_dist = float('inf')
                best_color = None
                
                for orig_color in color_map:
                    dist = np.sum((pixel - orig_color) ** 2)
                    if dist < min_dist:
                        min_dist = dist
                        best_color = color_map[orig_color]
                
                resized_pixels[i] = best_color

            # Reshape back to image dimensions
            final_image = resized_pixels.reshape((new_height, new_width, 3))

            # Update dimensions
            self.iAmendedHeight = new_height
            self.iAmendedWidth = new_width
            self.iAmendedMaxStitches = max(self.iAmendedWidth, self.iAmendedHeight)
            
            # Update amended image and its color count
            self.amended_image = final_image.astype(np.uint8)
            self.amended_image_colours = len(unique_colors)  # Maintain original color count
            
            # Clear redo history
            self.future.clear()

        except Exception as e:
            print(f"Error in change_size: {str(e)}")
            traceback.print_exc()

    def old_vec_change_size(self, size):
        """
        High-quality image resizing with vectorization and advanced preprocessing.
        """
        try:
            # Store current image in history
            self.history.append(self.amended_image.copy())
            
            # Get dimensions and calculate scaling
            height, width = self.amended_image.shape[:2]
            if width > height:
                scale_factor = size / width
            else:
                scale_factor = size / height

            # Calculate new dimensions
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)

            # Create temporary SVG path from image edges
            # This helps preserve sharp edges during resizing
            gray = cv2.cvtColor(self.amended_image, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, threshold1=30, threshold2=100)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Create vector mask
            vector_mask = np.zeros_like(gray)
            cv2.drawContours(vector_mask, contours, -1, 255, 1)

            # Apply bilateral filter to preserve edges while smoothing
            smoothed = cv2.bilateralFilter(self.amended_image, 9, 75, 75)
            
            # Convert to LAB color space for better color preservation
            lab_image = cv2.cvtColor(smoothed, cv2.COLOR_BGR2LAB)
            
            # Process each channel separately with edge-aware resizing
            channels = cv2.split(lab_image)
            resized_channels = []
            
            for channel in channels:
                # Combine channel with edge information
                edge_aware = cv2.addWeighted(channel, 0.7, vector_mask, 0.3, 0)
                
                # Multi-step resizing for large downscaling
                if scale_factor < 0.5:
                    steps = min(3, int(1 / scale_factor))
                    current_img = edge_aware
                    for step in range(steps):
                        if step == steps - 1:
                            # Final resize to target size
                            resized = cv2.resize(current_img, 
                                            (new_width, new_height),
                                            interpolation=cv2.INTER_LANCZOS4)
                        else:
                            # Intermediate resize
                            scale = 1 / (2 ** (step + 1))
                            w = int(width * scale)
                            h = int(height * scale)
                            current_img = cv2.resize(current_img,
                                                (w, h),
                                                interpolation=cv2.INTER_LANCZOS4)
                else:
                    # Direct resize for small size reductions
                    resized = cv2.resize(edge_aware,
                                    (new_width, new_height),
                                    interpolation=cv2.INTER_LANCZOS4)
                
                resized_channels.append(resized)
            
            # Merge channels and convert back to BGR
            resized_lab = cv2.merge(resized_channels)
            resized_image = cv2.cvtColor(resized_lab, cv2.COLOR_LAB2BGR)
            
            # Apply selective sharpening to maintain edge clarity
            blurred = cv2.GaussianBlur(resized_image, (0, 0), 3)
            sharpened = cv2.addWeighted(resized_image, 1.5, blurred, -0.5, 0)
            
            # Blend sharpened image with original resized image
            final_image = cv2.addWeighted(resized_image, 0.7, sharpened, 0.3, 0)

            # Update dimensions
            self.iAmendedHeight = new_height
            self.iAmendedWidth = new_width
            self.iAmendedMaxStitches = max(self.iAmendedWidth, self.iAmendedHeight)
            
            # Update amended image and color count
            self.amended_image = final_image
            self.amended_image_colours = self.get_number_of_colors(final_image)
            
            # Clear redo history
            self.future.clear()

        except Exception as e:
            print(f"Error in change_size: {str(e)}")
            traceback.print_exc()

    def change_size(self, size):
        """
        High-quality image resizing with color preservation in BGR color space.
        """
        try:
            # Store current image in history
            self.history.append(self.amended_image.copy())
            
            # Get dimensions and calculate scaling
            height, width = self.amended_image.shape[:2]
            if width > height:
                scale_factor = size / width
            else:
                scale_factor = size / height

            # Calculate new dimensions
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)

            if scale_factor < 0.5:
                # For large size reductions, use progressive approach
                
                # Split BGR channels
                b, g, r = cv2.split(self.amended_image)
                
                # Calculate number of steps
                steps = min(4, max(2, int(1 / scale_factor)))
                current_b = b
                current_g = g
                current_r = r
                
                # Progressive resizing
                for i in range(steps):
                    if i == steps - 1:
                        # Final target size
                        target_width = new_width
                        target_height = new_height
                    else:
                        # Intermediate step size
                        factor = (1 - (i + 1) / steps) 
                        target_width = int(width * (1 - factor * (1 - scale_factor)))
                        target_height = int(height * (1 - factor * (1 - scale_factor)))
                    
                    # Resize each channel separately
                    current_b = cv2.resize(
                        current_b, 
                        (target_width, target_height),
                        interpolation=cv2.INTER_AREA
                    )
                    current_g = cv2.resize(
                        current_g,
                        (target_width, target_height),
                        interpolation=cv2.INTER_AREA
                    )
                    current_r = cv2.resize(
                        current_r,
                        (target_width, target_height),
                        interpolation=cv2.INTER_AREA
                    )

                # Merge channels
                resized_image = cv2.merge([current_b, current_g, current_r])
                
            else:
                # For minor reductions, use direct approach
                resized_image = cv2.resize(
                    self.amended_image,
                    (new_width, new_height),
                    interpolation=cv2.INTER_AREA
                )

            # Apply sharpening in BGR space
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]]) / 9
            sharpened = cv2.filter2D(resized_image, -1, kernel)
            
            # Blend sharpened with original
            final_image = cv2.addWeighted(resized_image, 0.85, sharpened, 0.15, 0)

            # Update dimensions
            self.iAmendedHeight = new_height
            self.iAmendedWidth = new_width
            self.iAmendedMaxStitches = max(self.iAmendedWidth, self.iAmendedHeight)
            
            # Update amended image and color count
            self.amended_image = final_image
            self.amended_image_colours = self.get_number_of_colors(final_image)
            
            # Clear redo history
            self.future.clear()

        except Exception as e:
            print(f"Error in change_size: {str(e)}")
            traceback.print_exc()

    def undo(self):
        print("In ImageProcessor.undo()")
        if len(self.history) > 1:
            self.future.append(self.history.pop())
            self.amended_image = self.history[-1].copy()
            self.amended_image_colours=self.get_number_of_colors(self.amended_image)

    def redo(self):
        print("In ImageProcessor.redo()")
        if self.future:
            self.history.append(self.future.pop())
            self.amended_image = self.history[-1].copy()
            self.amended_image_colours=self.get_number_of_colors(self.amended_image)

    def get_number_of_colors(self, image):

        print("In ImageProcessor.get_number_of_colors()")
        
        if self.image_loaded==False:
            print("Image == None: No colours")
            return 0
        else:
            return len(np.unique(image.reshape(-1, image.shape[2]), axis=0))

    def update_color(self, image, old_color, new_color):

        #print("In ImageProcessor.update_color(). old_color: %s new_color: %s" % (str(old_color), str(new_color)))
        # Convert the old and new colors from RGB to BGR since OpenCV uses BGR
        old_color_bgr = (old_color[2], old_color[1], old_color[0])
        new_color_bgr = (new_color[2], new_color[1], new_color[0])
        
        # Create a mask that matches the old color
        mask = cv2.inRange(image, np.array(old_color_bgr), np.array(old_color_bgr))
            
        # Update the color in the image
        image[mask != 0] = new_color_bgr

        return image

    def get_pixel_intensity(self, x, y):
        """
        Determines the intensity (brightness) of a pixel in the amended image.
        Returns value between 0 (black) and 255 (white).
        """
        if not self.amended_image.any():
            return 0
            
        # Convert pixel to grayscale if it isn't already
        pixel = self.amended_image[y, x]
        if len(pixel) == 3:  # If BGR color
            # Convert to grayscale using standard weights
            intensity = int(0.299 * pixel[2] + 0.587 * pixel[1] + 0.114 * pixel[0])
        else:  # Already grayscale
            intensity = pixel
            
        return intensity

    def get_tile_intensity(self, tile):
        """
        Determines the intensity of a vector tile by calculating the ratio of black pixels
        to total area in a 10x10 tile.
        Returns value between 0 (completely black) and 255 (completely white).
        """
        # Create blank 10x10 white image
        tile_image = np.full((10, 10), 255, dtype=np.uint8)
        
        # Draw each stitch as a black line with proper thickness
        total_pixels = 10 * 10
        black_pixels = 0
        
        for stitch in tile['stitches']:
            # Calculate relative coordinates within tile
            start_x = int(stitch.stitchStart.x())
            start_y = int(stitch.stitchStart.y())
            end_x = int(stitch.stitchEnd.x())
            end_y = int(stitch.stitchEnd.y())
            
            # Draw line on temporary image
            cv2.line(
                tile_image,
                (start_x, start_y),
                (end_x, end_y),
                0,  # Black color
                thickness=stitch.weight
            )
        
        # Count black pixels
        black_pixels = np.count_nonzero(tile_image == 0)
        
        # Calculate intensity (invert the ratio since more black = lower intensity)
        intensity = int(255 * (1 - (black_pixels / total_pixels)))
        
        return intensity

class FlossImageLabelDialog(QDialog): 
    def __init__(self, image, parent=None): 
        super().__init__(parent)
        self.setWindowTitle('Floss Image') 
        self.layout = QVBoxLayout(self) 
        self.image_label = QLabel(self) 
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("background-color: lightgray; border: 1px solid black;")
        self.image_label.setFixedSize(300,300)
        self.layout.addWidget(self.image_label)
        self.set_image(image)
        #pixmap = QPixmap.fromImage(image) 
        #self.image_label.setPixmap(pixmap) 
        

    def set_image(self, image):
        print("In FlossImageLabelDialog.set_image()")

        label=self.image_label
        height, width, channel = image.shape
        bytes_per_line = 3 * width
        q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_BGR888)
        pixmap = QPixmap.fromImage(q_image)
        label.setPixmap(pixmap.scaled(label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))

class FlossesWindow(QMainWindow):
    def __init__(self, allBrands):
        super().__init__()
        self.setWindowTitle("Flosses")
        self.setGeometry(100, 100, 600, 800)
        self.allBrands = allBrands
        self.current_brand = None

        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # Create the toolbar
        toolbar = QToolBar("Toolbar")
        self.addToolBar(toolbar)

        # Add floss action
        add_action = QAction(QIcon("add_icon.png"), "Add Floss", self)
        add_action.triggered.connect(self.add_new_floss)
        toolbar.addAction(add_action)

        # Save flosses action
        save_action = QAction(QIcon("save_icon.png"), "Save Flosses", self)
        save_action.triggered.connect(self.save_flosses)
        toolbar.addAction(save_action)

        # Create brand dropdown
        self.brand_dropdown = QComboBox()
        self.brand_dropdown.addItems(self.allBrands.getListOfBrandNames())
        self.brand_dropdown.currentIndexChanged.connect(self.load_flosses)
        toolbar.addWidget(self.brand_dropdown)

        # Create header widget
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        headers = ["Name", "Colour Name", "Colour", "RGB", "Hex", "CMYK"]
        for header in headers:
            header_label = QLabel(header)
            header_label.setAlignment(Qt.AlignCenter)
            header_layout.addWidget(header_label)
        main_layout.addWidget(header_widget)

        # Create the list widget
        self.floss_list_widget = QListWidget()
        main_layout.addWidget(self.floss_list_widget)

        # Load the initial flosses for the default selected brand
        self.load_flosses()

    def load_flosses(self):
        self.current_brand = self.brand_dropdown.currentText()
        print("FlossesWindow.load_flosses(): self.current_brand: ", self.current_brand)
        self.floss_list_widget.clear()

        # Add header row again 
        header_widget = QWidget() 
        header_layout = QHBoxLayout() 
        headers = ["Name", "Colour Name", "Colour", "RGB", "Hex", "CMYK"] 
        for header in headers: 
            header_label = QLabel(header) 
            header_label.setAlignment(Qt.AlignCenter) 
            header_layout.addWidget(header_label) 
        header_widget.setLayout(header_layout) 
        self.floss_list_widget.addItem(QListWidgetItem()) 
        self.floss_list_widget.setItemWidget(self.floss_list_widget.item(0), header_widget) 
       

        check_object_type(self.allBrands.brands)
        for brand in self.allBrands.brands:
            #check_object_type(brand)
        
            if brand.name ==  self.current_brand:
                print("Brand match found: %s" % (brand.name))
                for floss in brand.flosses:
                    #floss.printFloss()
                    self.add_floss_to_list(floss)
                break

    def add_new_floss(self):
        if not self.current_brand:
            QMessageBox.warning(self, "No Brand Selected", "Please select a brand before adding a floss.")
            return

        name, ok1 = QInputDialog.getText(self, "Add Floss", "Enter Floss Name:")
        if not ok1 or not name:
            return

        color_name, ok2 = QInputDialog.getText(self, "Add Floss", "Enter Colour Name:")
        if not ok2 or not color_name:
            return

        rgb_str, ok3 = QInputDialog.getText(self, "Add Floss", "Enter RGB Value (e.g., 255,255,255):")
        if ok3 and rgb_str:
            try:
                rgb = list(map(int, rgb_str.split(',')))
                if len(rgb) != 3:
                    raise ValueError
            except ValueError:
                QMessageBox.warning(self, "Invalid RGB Value", "Please enter a valid RGB value (e.g., 255,255,255).")
                return

            floss = Floss(self.current_brand, name, color_name, rgb)
            self.add_floss_to_list(floss)
            self.flosses_data.append(floss)
        else:
            return

    def add_floss_to_list(self, floss):
        item = QListWidgetItem()
        item_widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # Floss name editor
        name_edit = QLineEdit(floss.name)
        layout.addWidget(name_edit)

        # Colour name editor
        color_name_edit = QLineEdit(floss.color_name)
        layout.addWidget(color_name_edit)

        # Color block
        color_label = QLabel()
 
class FlossBrandsWindow(QMainWindow):
    def __init__(self, allBrands):
        super().__init__()
        self.setWindowTitle("Floss Brands")
        self.setGeometry(100, 100, 400, 600)
        self.allBrands = allBrands

        # Create the toolbar
        toolbar = QToolBar("Toolbar")
        self.addToolBar(toolbar)

        # Add brand action
        add_action = QAction(QIcon("add_icon.svg"), "Add Brand", self)
        add_action.triggered.connect(self.add_brand)
        toolbar.addAction(add_action)

        # Save brands action
        #save_action = QAction(QIcon("save_icon.svg"), "Save Brands", self)
        #save_action.triggered.connect(self.save_brands)
        #toolbar.addAction(save_action)

        # Create the list widget
        self.brand_list_widget = QListWidget()
        self.setCentralWidget(self.brand_list_widget)

        for brand in allBrands.brands:
            self.add_brand_to_list(brand.name)

    def add_brand(self):
        text, ok = QInputDialog.getText(self, "Add Brand", "Enter Floss Brand Name:")
        if ok and text:
            self.add_brand_to_list(text)
            newBrand=br.Brand(text)
            self.allBrands.addBrand(newBrand)

    def add_brand_to_list(self, brand_name):
        item = QListWidgetItem()
        item_widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # Brand name editor
        brand_edit = QLineEdit(brand_name)
        layout.addWidget(brand_edit)

        # Delete brand button
        delete_button = QPushButton()
        delete_button.setIcon(QIcon("delete_icon.svg"))
        delete_button.clicked.connect(lambda: self.delete_brand(item))
        layout.addWidget(delete_button)

        item_widget.setLayout(layout)
        item.setSizeHint(item_widget.sizeHint())
        self.brand_list_widget.addItem(item)
        self.brand_list_widget.setItemWidget(item, item_widget)

    def delete_brand(self, item):
        row = self.brand_list_widget.row(item)
        self.brand_list_widget.takeItem(row)

    def save_brands(self):
        brands = []
        for i in range(self.brand_list_widget.count()):
            item = self.brand_list_widget.item(i)
            widget = self.brand_list_widget.itemWidget(item)
            line_edit = widget.findChild(QLineEdit)
            brands.append(line_edit.text())

        with open("floss_brands.dat", "wb") as file:
            pickle.dump(brands, file)
        
        # Update the shared structure
        self.brands_data.clear()
        self.brands_data.extend(brands)
        
        QMessageBox.information(self, "Save Brands", "Floss brands saved successfully.")

class PatternWindow(QMainWindow): 
    def __init__(self, processor): 
        super().__init__() 
        self.setWindowTitle("Pattern") 
        self.setGeometry(300, 300, 800, 600)
        # Create the menu bar
        menu_bar = self.menuBar()
        self.processor = processor

        # Create menu items


        # Ensure menu bar shows up properly on macOS
        self.setMenuBar(menu_bar)


        # Create the icon toolbar
        icon_toolbar = QToolBar("Icon Bar")
        self.addToolBar(icon_toolbar)

        # Create toolbar items
        

        # Create central widget with an image box
        central_widget = QWidget()
        layout = QHBoxLayout()

        # Image boxes
        self.pat_image_label_floss = QLabel(self)
        self.pat_image_label_floss.setAlignment(Qt.AlignCenter)
        self.pat_image_label_floss.setStyleSheet("background-color: lightgray; border: 1px solid black;")

        #self.set_image(self.processor.process_image(self.pat_image_label_floss))
    
        self.pat_image_label_pattern = QLabel("No Image Opened")
        self.pat_image_label_pattern.setAlignment(Qt.AlignCenter)
        self.pat_image_label_pattern.setStyleSheet("background-color: lightgray; border: 1px solid black;")

        layout.addWidget(self.pat_image_label_floss)
        layout.addWidget(self.pat_image_label_pattern)
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)

        # Create the status bar with image size info
        status_bar = QStatusBar()
        self.image_size_label = QLabel("Image size: ")
        status_bar.addWidget(self.image_size_label)
        self.setStatusBar(status_bar)

        # Create an Image object
        #self.image_data = Image()

    def set_image(self, image):
        height, width, channel = image.shape
        bytes_per_line = 3 * width
        q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_BGR888)
        pixmap = QPixmap.fromImage(q_image)
        scaled_pixmap = pixmap.scaled(self.image_label.size(), Qt.KeepAspectRatio)
        self.image_label.setPixmap(scaled_pixmap)

    def update_image(self, image):
        self.set_image(image)

# Can this be a QDialog in the future?
class AssignFlossesWindow(QMainWindow):
    update_image_labels_signal = Signal()
    result_signal = Signal(bool)

    def __init__(self, processor, brands, project):
        super().__init__() 
        self.setWindowTitle("Assign Flosses") 
        self.setGeometry(200, 200, 900, 700)
        self.image= processor.amended_image
        self.floss_image=processor.amended_image.copy()
        self.processor=processor        
        self.all_brands = brands
        self.project=project
        self.floss_image_dialog = FlossImageLabelDialog(self.floss_image, self)
        self.floss_image_dialog.show()
        self.bAmended=False
        print("In assignFlosses()")

        self.all_flosses = [] 
        for brand in allBrands.brands: 
            for floss in brand.flosses: 
                self.all_flosses.append(floss)

        central_widget = QWidget() 
        self.setCentralWidget(central_widget) 
        main_layout = QVBoxLayout()

        # Create table for image colors
        self.color_table = QTableWidget(0, 8)
        self.color_table.setHorizontalHeaderLabels([
            'RGB Value', '# Stitches', 'Original Colour', 'Floss', 'Alternative Name', 'New Floss',
            'Print 1', 'Print 2'])
        self.color_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        table_layout = QHBoxLayout()
        table_layout.addWidget(self.color_table)
        button_layout = QHBoxLayout()

        # Add save button
        buttonSave = QPushButton('Save and exit')
        buttonSave.clicked.connect(self.save_flosses)
        button_layout.addWidget(buttonSave)
   
        # Add exit button
        buttonExit = QPushButton('Exit')
        buttonExit.clicked.connect(self.exit_without_saving)
        button_layout.addWidget(buttonExit)
        
        # Get unique colors and their counts
        print("In AssignFlosses(). useColour: %d" % (self.project.use_colour))
        if self.project.use_colour:
            orig_colors, counts = np.unique(self.image.reshape(-1, self.image.shape[2]), axis=0, return_counts=True)
        
            for color, count in zip(orig_colors, counts): 
                self.add_color_row(color, count)
        else:
            self.add_color_row([0,0,0],1)
        self.brand_names = self.all_brands.getListOfBrandNames()
           

        main_layout.addLayout(table_layout)
        main_layout.addLayout(button_layout)
        
        central_widget.setLayout(main_layout)
        #self.adjustSize()

        #if self.project.useColour:
        self.match_flosses()
        self.distinct_colours(len(self.all_flosses))  # Auto-populate print colors

    def distinct_colours(self, num_colors):
        """Generate visually distinct color sets that are accessible."""
        
        def is_special_color(rgb):
            """Check if color is black or white"""
            return rgb in [(0,0,0), (255,255,255)]
        
        def rgb_to_string(rgb):
            """Convert RGB tuple to string format"""
            return f"{rgb[0]},{rgb[1]},{rgb[2]}"
        
        def generate_distinct_color(existing_colors):
            """Generate a new color that is maximally distinct from existing colors"""
            if not existing_colors:
                return (0, 121, 231)  # Start with blue
                
            def color_distance(c1, c2):
                """Calculate perceptual color distance"""
                # Using weighted Euclidean distance for better perceptual distinction
                r_weight = 0.3
                g_weight = 0.59
                b_weight = 0.11
                return ((r_weight * (c1[0] - c2[0]))**2 + 
                    (g_weight * (c1[1] - c2[1]))**2 + 
                    (b_weight * (c1[2] - c2[2]))**2)**0.5
            
            # Define color space boundaries
            max_attempts = 1000
            best_distance = 0
            best_color = None
            
            # Try to find maximally distinct color
            for _ in range(max_attempts):
                # Generate candidate color with good saturation and brightness
                h = random.random()  # Hue: 0-1
                s = random.uniform(0.5, 1.0)  # Saturation: 0.5-1.0
                v = random.uniform(0.5, 1.0)  # Value: 0.5-1.0
                
                # Convert HSV to RGB
                r, g, b = colorsys.hsv_to_rgb(h, s, v)
                candidate = (int(r * 255), int(g * 255), int(b * 255))
                
                # Calculate minimum distance to existing colors
                min_distance = float('inf')
                for existing in existing_colors:
                    dist = color_distance(candidate, existing)
                    min_distance = min(min_distance, dist)
                
                # Update best if this is more distinct
                if min_distance > best_distance:
                    best_distance = min_distance
                    best_color = candidate
            
            return best_color
        
        # Get all floss colors from the table
        floss_colors = {}
        for row in range(self.color_table.rowCount()):
            floss_container = self.color_table.cellWidget(row, 3)
            if floss_container:
                floss_name_label = floss_container.findChild(QLabel)
                floss_name = floss_name_label.text() if floss_name_label else None
                
                if floss_name:
                    item = self.color_table.item(row, 0)
                    if item:
                        rgb = tuple(map(int, item.text().split(', ')))
                        floss_colors[floss_name] = rgb
        
        # Generate distinct colors for each set
        print1_assignments = {}
        print2_assignments = {}
        print1_used_colors = []
        print2_used_colors = []
        
        for floss_name, rgb in floss_colors.items():
            # Skip background color
            if rgb == tuple(self.project.background_colour):
                continue
                
            # Preserve black and white
            if is_special_color(rgb):
                print1_assignments[floss_name] = rgb
                print2_assignments[floss_name] = rgb
                print1_used_colors.append(rgb)
                print2_used_colors.append(rgb)
                continue
            
            # Generate distinct colors for each set
            print1_color = generate_distinct_color(print1_used_colors)
            print2_color = generate_distinct_color(print2_used_colors)
            
            print1_assignments[floss_name] = print1_color
            print2_assignments[floss_name] = print2_color
            
            print1_used_colors.append(print1_color)
            print2_used_colors.append(print2_color)
        
        # Update the table with the assigned colors
        for row in range(self.color_table.rowCount()):
            floss_container = self.color_table.cellWidget(row, 3)
            if floss_container:
                floss_name_label = floss_container.findChild(QLabel)
                floss_name = floss_name_label.text() if floss_name_label else None
                
                if floss_name and floss_name in print1_assignments:
                    # Update Print Color 1
                    print_color1_container = self.color_table.cellWidget(row, 6)
                    if print_color1_container:
                        print_color1_edit = print_color1_container.findChild(QLineEdit)
                        print_color1_display = print_color1_container.findChild(QLabel)
                        if not print_color1_edit.text():  # Only update if empty
                            rgb = print1_assignments[floss_name]
                            print_color1_edit.setText(rgb_to_string(rgb))
                            self.update_print_color_display(print_color1_edit, print_color1_display)
                    
                    # Update Print Color 2
                    print_color2_container = self.color_table.cellWidget(row, 7)
                    if print_color2_container:
                        print_color2_edit = print_color2_container.findChild(QLineEdit)
                        print_color2_display = print_color2_container.findChild(QLabel)
                        if not print_color2_edit.text():  # Only update if empty
                            rgb = print2_assignments[floss_name]
                            print_color2_edit.setText(rgb_to_string(rgb))
                            self.update_print_color_display(print_color2_edit, print_color2_display)

    def old_distinct_colours(self, num_colors):
        """Generate visually distinct color sets that are accessible."""
        
        # Define color palettes that avoid red-green combinations
        print1_base_colors = [
            (0, 0, 0),      # Black
            (255, 255, 255), # White
            (0, 121, 231),   # Blue
            (255, 127, 0),   # Orange
            (255, 200, 0),   # Yellow
            (128, 0, 128),   # Purple
            (0, 168, 168),   # Cyan
            (255, 105, 180), # Pink
            (165, 42, 42),   # Brown
            (106, 90, 205)   # Slate blue
        ]
        
        print2_base_colors = [
            (0, 0, 0),      # Black
            (255, 255, 255), # White
            (255, 69, 0),    # Red-orange
            (147, 112, 219), # Medium purple
            (60, 179, 113),  # Medium sea green
            (30, 144, 255),  # Dodger blue
            (218, 112, 214), # Orchid
            (184, 134, 11),  # Dark goldenrod
            (70, 130, 180),  # Steel blue
            (139, 69, 19)    # Saddle brown
        ]
        
        def is_special_color(rgb):
            """Check if color is black or white"""
            return rgb in [(0,0,0), (255,255,255)]
        
        def rgb_to_string(rgb):
            """Convert RGB tuple to string format"""
            return f"{rgb[0]},{rgb[1]},{rgb[2]}"
        
        # Get all floss colors from the table
        floss_colors = {}
        for row in range(self.color_table.rowCount()):
            floss_container = self.color_table.cellWidget(row, 3)
            if floss_container:
                floss_name_label = floss_container.findChild(QLabel)
                floss_name = floss_name_label.text() if floss_name_label else None
                
                if floss_name:
                    item = self.color_table.item(row, 0)
                    if item:
                        rgb = tuple(map(int, item.text().split(', ')))
                        floss_colors[floss_name] = rgb
        
        # Assign colors for each floss
        print1_assignments = {}
        print2_assignments = {}
        
        for floss_name, rgb in floss_colors.items():
            # Skip background color
            if rgb == tuple(self.project.background_colour):
                continue
                
            # Preserve black and white
            if is_special_color(rgb):
                print1_assignments[floss_name] = rgb
                print2_assignments[floss_name] = rgb
                continue
                
            # Assign distinct colors from the palettes
            idx = len(print1_assignments) % len(print1_base_colors)
            print1_assignments[floss_name] = print1_base_colors[idx]
            print2_assignments[floss_name] = print2_base_colors[idx]
        
        # Update the table with the assigned colors
        for row in range(self.color_table.rowCount()):
            floss_container = self.color_table.cellWidget(row, 3)
            if floss_container:
                floss_name_label = floss_container.findChild(QLabel)
                floss_name = floss_name_label.text() if floss_name_label else None
                
                if floss_name and floss_name in print1_assignments:
                    # Update Print Color 1
                    print_color1_container = self.color_table.cellWidget(row, 6)
                    if print_color1_container:
                        print_color1_edit = print_color1_container.findChild(QLineEdit)
                        print_color1_display = print_color1_container.findChild(QLabel)
                        if not print_color1_edit.text():  # Only update if empty
                            rgb = print1_assignments[floss_name]
                            print_color1_edit.setText(rgb_to_string(rgb))
                            self.update_print_color_display(print_color1_edit, print_color1_display)
                    
                    # Update Print Color 2
                    print_color2_container = self.color_table.cellWidget(row, 7)
                    if print_color2_container:
                        print_color2_edit = print_color2_container.findChild(QLineEdit)
                        print_color2_display = print_color2_container.findChild(QLabel)
                        if not print_color2_edit.text():  # Only update if empty
                            rgb = print2_assignments[floss_name]
                            print_color2_edit.setText(rgb_to_string(rgb))
                            self.update_print_color_display(print_color2_edit, print_color2_display)

    def reset_state(self):
        """Reset window state for new project"""
        # Clear the color table
        self.color_table.setRowCount(0)
        
        # Reset the floss image
        self.floss_image = self.processor.amended_image.copy()
        self.floss_image_dialog.set_image(self.floss_image)
        
        # Clear any cached data
        self.bAmended = False
        
        # Reset all flosses list
        self.all_flosses = []
        for brand in self.all_brands.brands:
            for floss in brand.flosses:
                self.all_flosses.append(floss)

    def get_unique_colors(self, image):
        """Extract unique colors from the image"""
        # Reshape the image to a 2D array of pixels
        pixels = image.reshape(-1, 3)
        
        # Convert to a set of tuples for unique colors
        unique_colors = set(map(tuple, pixels))
        
        # Convert back to a list and sort for consistent display
        return sorted(list(unique_colors))
    def save_flosses(self):
        print("In assignFlossWindow.save_flosses()")

        # read each RGB, floss name and floss RGB from the table
        for row in range(self.color_table.rowCount()):
            # Get floss container and extract name from label
            floss_container = self.color_table.cellWidget(row, 3)
            floss_name_label = floss_container.findChild(QLabel)
            floss_name = floss_name_label.text() if floss_name_label else None

            # Get RGB values
            itemRGB = self.color_table.item(row, 0)
            itemFlossRGB = self.color_table.item(row, 5)
            
            if floss_name and itemRGB and itemFlossRGB:
                origRGB = itemRGB.text()
                flossRGB = itemFlossRGB.text()

                # find the floss and add it to the project
                newFloss = self.all_brands.findFlossFromName(floss_name)
                if newFloss:
                    self.project.addFloss(newFloss)
                    
        self.project.dtFlossesAssigned = datetime.datetime.now()

        # Save print colors
        self.project.print_colors.clear()
        for row in range(self.color_table.rowCount()):
            # Get floss name from container widget
            floss_container = self.color_table.cellWidget(row, 3)
            floss_name_label = floss_container.findChild(QLabel)
            floss_name = floss_name_label.text() if floss_name_label else None

            if floss_name:
                # Get print color widgets
                print_color1_container = self.color_table.cellWidget(row, 6)
                print_color2_container = self.color_table.cellWidget(row, 7)
                
                # Get color values from edit fields
                print_color1 = print_color1_container.findChild(QLineEdit).text() if print_color1_container else ""
                print_color2 = print_color2_container.findChild(QLineEdit).text() if print_color2_container else ""
                
                # Save to project
                self.project.print_colors[floss_name] = (print_color1, print_color2)

        # Update the amended image
        self.processor.amended_image = self.floss_image.copy()
        self.processor.history.append(self.processor.amended_image.copy())
        self.processor.amended_image_colours = self.processor.get_number_of_colors(self.processor.amended_image)
        
        # Update main window and close
        self.update_image_labels_signal.emit()
        self.result_signal.emit(True)
        self.floss_image_dialog.close()
        self.close()
        
    def refresh_colors(self, amended_image):
        """Refresh the floss assignments based on new image colors"""
        # Update the internal image reference
        self.amended_image = amended_image
        
        # Extract unique colors from the amended image
        self.unique_colors = self.get_unique_colors(amended_image)
        
        # Preserve existing assignments that are still valid
        new_color_floss_map = {}
        for color in self.unique_colors:
            if color in self.color_floss_map:
                new_color_floss_map[color] = self.color_floss_map[color]
        
        self.color_floss_map = new_color_floss_map
        
        # Update project's assigned flosses
        self.main_window.project.assigned_flosses = self.color_floss_map.copy()
        
        # Clear existing color widgets
        for i in reversed(range(self.colors_layout.count())): 
            self.colors_layout.itemAt(i).widget().setParent(None)
        
        # Recreate color widgets for new colors
        self.create_color_widgets()
        
        # Update the window and info pane
        self.update()
        self.main_window.update_info_pane()
    def exit_without_saving(self):
        # Close down the window
        self.result_signal.emit(False)
        self.floss_image_dialog.close()
        self.close()

    def closeEvent(self, event):        
        self.floss_image_dialog.close()
        event.accept()

    def open_filter_dialog(self, brands):
        dialog = FilterDialog(self, brands)
        if dialog.exec():
            checkedBrands = dialog.get_checked_brands()
            print('Checked items:', checkedBrands)
        dialog.exec()
        return checkedBrands

    def add_color_row(self, color, count):
        row_position = self.color_table.rowCount()
        self.color_table.insertRow(row_position)
        self.color_table.setRowHeight(row_position, 40)
        
        # RGB Value
        rgb_value = f'{color[2]}, {color[1]}, {color[0]}'
        self.color_table.setItem(row_position, 0, QTableWidgetItem(rgb_value))
        
        # Number of Stitches
        self.color_table.setItem(row_position, 1, QTableWidgetItem(str(count)))
        
        # Original Colour block
        color_label = QLabel()
        color_label.setFixedSize(50, 50)
        color_label.setStyleSheet(f'background-color: rgb({color[2]}, {color[1]}, {color[0]});')
        self.color_table.setCellWidget(row_position, 2, color_label)

        # Floss container (column 3)
        floss_container = QWidget()
        floss_layout = QHBoxLayout(floss_container)
        floss_layout.setContentsMargins(4, 4, 4, 4)
        floss_name_label = QLabel("")
        floss_name_label.setFixedWidth(100)
        floss_color_display = QLabel()
        floss_color_display.setFixedSize(20, 20)
        floss_color_display.setStyleSheet("background-color: white; border: 1px solid black")
        floss_layout.addWidget(floss_name_label)
        floss_layout.addWidget(floss_color_display)
        self.color_table.setCellWidget(row_position, 3, floss_container)

        # Alternative Name (column 4)
        self.color_table.setItem(row_position, 4, QTableWidgetItem(''))

        # New Floss field (column 5)
        floss_edit = QLineEdit()
        floss_edit.editingFinished.connect(lambda f=floss_edit, r=row_position: self.validate_floss(f, r))
        self.color_table.setCellWidget(row_position, 5, floss_edit)

        # Print Color 1 container (column 6)
        print_color1_container = QWidget()
        print_color1_layout = QHBoxLayout(print_color1_container)
        print_color1_layout.setContentsMargins(4, 4, 4, 4)
        print_color1_edit = QLineEdit()
        print_color1_display = QLabel()
        print_color1_display.setFixedSize(20, 20)
        print_color1_display.setStyleSheet("background-color: white; border: 1px solid black")
        print_color1_edit.editingFinished.connect(
            lambda: self.update_print_color_display(print_color1_edit, print_color1_display))
        print_color1_layout.addWidget(print_color1_edit)
        print_color1_layout.addWidget(print_color1_display)
        self.color_table.setCellWidget(row_position, 6, print_color1_container)

        # Print Color 2 container (column 7)
        print_color2_container = QWidget()
        print_color2_layout = QHBoxLayout(print_color2_container)
        print_color2_layout.setContentsMargins(4, 4, 4, 4)
        print_color2_edit = QLineEdit()
        print_color2_display = QLabel()
        print_color2_display.setFixedSize(20, 20)
        print_color2_display.setStyleSheet("background-color: white; border: 1px solid black")
        print_color2_edit.editingFinished.connect(
            lambda: self.update_print_color_display(print_color2_edit, print_color2_display))
        print_color2_layout.addWidget(print_color2_edit)
        print_color2_layout.addWidget(print_color2_display)
        self.color_table.setCellWidget(row_position, 7, print_color2_container)

    def validate_floss(self, floss_edit, row_position):
        floss_name = floss_edit.text()
        matched_floss = next((floss for floss in self.all_flosses if floss.name == floss_name), None)

        # Special handling for Background Fabric
        if floss_name == "Background Fabric":
            rgb_value = list(map(int, self.color_table.item(row_position, 0).text().split(', ')))

            # Update project background color - the RGB values need to be in RGB order, not BGR
            self.project.background_colour = (rgb_value[2], rgb_value[1], rgb_value[0])

            # Update floss container
            floss_container = self.color_table.cellWidget(row_position, 3)
            floss_name_label = floss_container.findChild(QLabel)
            floss_color_display = floss_container.findChildren(QLabel)[1]
            
            floss_name_label.setText("Background Fabric")
            floss_color_display.setStyleSheet(
                f'background-color: rgb({rgb_value[0]}, {rgb_value[1]}, {rgb_value[2]}); '
                'border: 1px solid black'
            )
            
            # Update other columns
            self.color_table.setItem(row_position, 4, QTableWidgetItem("Background"))
            self.color_table.setItem(row_position, 5, QTableWidgetItem("Background Fabric"))
            return
        
            # Mark project as having unsaved changes
            #self.project.bUnsavedChanges = True
            #return

        if matched_floss:
            # Get the original RGB color for this row only
            rgb_value = list(map(int, self.color_table.item(row_position, 0).text().split(', ')))

            # Update floss name and color display
            floss_container = self.color_table.cellWidget(row_position, 3)
            floss_name_label = floss_container.findChild(QLabel)
            floss_color_display = floss_container.findChildren(QLabel)[1]
                    
            # Update name label and color display
            floss_name_label.setText(matched_floss.name)
            floss_color_display.setStyleSheet(
                f'background-color: rgb({matched_floss.rgb[0]}, {matched_floss.rgb[1]}, {matched_floss.rgb[2]}); '
                'border: 1px solid black'
            )

            # Update other columns
            self.color_table.setItem(row_position, 4, QTableWidgetItem(matched_floss.color_name))
            self.color_table.setItem(row_position, 5, QTableWidgetItem(matched_floss.name))

            # Update print colors if they exist
            print_color1_container = self.color_table.cellWidget(row_position, 6)
            print_color2_container = self.color_table.cellWidget(row_position, 7)

            if print_color1_container and print_color2_container and matched_floss.name in self.project.print_colors:
                color1, color2 = self.project.print_colors[matched_floss.name]
                print_color1_edit = print_color1_container.findChild(QLineEdit)
                print_color2_edit = print_color2_container.findChild(QLineEdit)
                print_color1_display = print_color1_container.findChild(QLabel)
                print_color2_display = print_color2_container.findChild(QLabel)
                
                if color1:
                    print_color1_edit.setText(color1)
                    self.update_print_color_display(print_color1_edit, print_color1_display)
                if color2:
                    print_color2_edit.setText(color2)
                    self.update_print_color_display(print_color2_edit, print_color2_display)

            # Only update the specific color in the floss image
            if rgb_value != matched_floss.rgb:
                self.processor.update_color(self.floss_image, rgb_value, matched_floss.rgb)

            self.floss_image_dialog.set_image(self.floss_image)

    def create_floss_label(self, floss):
        #print("create_floss()")
        #print(floss.name, floss.color_name)
        #print("Name: %s    Alt-Name: %s" % (floss.name, floss.color_name))
        #floss_label = QLabel(f"{floss['name']}: {floss['alternative_name']}")
        floss_label = QLabel(f"{str(floss.name)}: {str(floss.color_name)}")
        floss_color = floss.rgb
        floss_label.setStyleSheet(f'background-color: rgb({floss_color[0]}, {floss_color[1]}, {floss_color[2]});')
        floss_label.setFixedSize(200, 50)
        return floss_label        

    def match_flosses(self):
        print("In assignFlossesWinow.match_flosses()")
        for row in range(self.color_table.rowCount()):
            item = self.color_table.item(row, 0)
            if item:
                rgb_value = list(map(int, item.text().split(', ')))

                # Check if this is explicitly the background fabric color
                is_background = tuple(rgb_value) == tuple(self.project.background_colour)

                # Check if this is a background color
                if is_background:
                    # Create a fabric "floss" entry
                    floss_container = self.color_table.cellWidget(row, 3)
                    floss_name_label = floss_container.findChild(QLabel)
                    floss_color_display = floss_container.findChildren(QLabel)[1]
                    
                    # Set the floss name to "Fabric" and use background color
                    floss_name_label.setText("Fabric")
                    floss_color_display.setStyleSheet(
                        f'background-color: rgb({rgb_value[0]}, {rgb_value[1]}, {rgb_value[2]}); '
                        'border: 1px solid black'
                    )

                    # Update alternative name and floss fields
                    self.color_table.setItem(row, 4, QTableWidgetItem("Background"))
                    self.color_table.setItem(row, 5, QTableWidgetItem("Background Fabric"))
                    continue

                closest_floss = self.find_closest_floss(rgb_value)
                
                # Update floss container with name and color
                floss_container = self.color_table.cellWidget(row, 3)
                floss_name_label = floss_container.findChild(QLabel)
                floss_color_display = floss_container.findChildren(QLabel)[1]
                
                # Set the floss name and update color display
                floss_name_label.setText(closest_floss.name)
                floss_color_display.setStyleSheet(
                    f'background-color: rgb({closest_floss.rgb[0]}, {closest_floss.rgb[1]}, {closest_floss.rgb[2]}); '
                    'border: 1px solid black'
                )

                # Update alternative name and floss fields
                self.color_table.setItem(row, 4, QTableWidgetItem(closest_floss.color_name))
                self.color_table.setItem(row, 5, QTableWidgetItem(closest_floss.name))

                # Get the print color widgets and update if needed
                print_color1_container = self.color_table.cellWidget(row, 6)
                print_color2_container = self.color_table.cellWidget(row, 7)

                if print_color1_container and print_color2_container and closest_floss.name in self.project.print_colors:
                    color1, color2 = self.project.print_colors[closest_floss.name]
                    print_color1_edit = print_color1_container.findChild(QLineEdit)
                    print_color2_edit = print_color2_container.findChild(QLineEdit)
                    print_color1_display = print_color1_container.findChild(QLabel)
                    print_color2_display = print_color2_container.findChild(QLabel)
                    
                    if color1:
                        print_color1_edit.setText(color1)
                        self.update_print_color_display(print_color1_edit, print_color1_display)
                    if color2:
                        print_color2_edit.setText(color2)
                        self.update_print_color_display(print_color2_edit, print_color2_display)

                # Update floss image
                if rgb_value != closest_floss.rgb:
                    self.processor.update_color(self.floss_image, rgb_value, closest_floss.rgb)

        self.floss_image_dialog.set_image(self.floss_image)

    def find_closest_floss(self, target_rgb):
        min_distance = float('inf')
        closest_floss = None
        #print(self.allFlosses)
        
        for floss in self.all_flosses:
            distance = np.linalg.norm(np.array(target_rgb) - np.array(floss.rgb))
            if distance < min_distance:
                min_distance = distance
                closest_floss = floss
        
        return closest_floss
    def assign_floss(self, rgb_color, floss):
        """Assign a floss to a specific RGB color"""
        # Update the assignment in the window
        self.color_floss_map[rgb_color] = floss
        
        # Update the project's floss assignments
        self.main_window.project.assigned_flosses[rgb_color] = floss
        
        # Update the info pane in the main window
        self.main_window.update_info_pane()

    def update_print_color_display(self, edit, label):
        """Update the color display box when print color is entered"""
        try:
                rgb_text = edit.text().strip()
                if rgb_text:
                    r, g, b = map(int, rgb_text.split(','))
                    if 0 <= r <= 255 and 0 <= g <= 255 and 0 <= b <= 255:
                        label.setStyleSheet(f"background-color: rgb({r},{g},{b}); border: 1px solid black")
                        
                        # Find the row this print color belongs to
                        container = edit.parent()
                        row = -1
                        for i in range(self.color_table.rowCount()):
                            if self.color_table.cellWidget(i, 8) == container or self.color_table.cellWidget(i, 9) == container:
                                row = i
                                break
                        
                        if row >= 0:
                            # Get the floss name from column 3
                            floss_name = self.color_table.item(row, 3).text()
                            if floss_name:
                                # Get both print colors
                                color1_container = self.color_table.cellWidget(row, 8)
                                color2_container = self.color_table.cellWidget(row, 9)
                                color1_edit = color1_container.findChild(QLineEdit)
                                color2_edit = color2_container.findChild(QLineEdit)
                                
                                # Save to project
                                self.project.print_colors[floss_name] = (color1_edit.text().strip(), color2_edit.text().strip())
                        
                        return
        except:
            pass
        label.setStyleSheet("background-color: white; border: 1px solid black")

class StitchCollection:
    def __init__(self):
        self._stitches = []

    def add_stitch(self, stitch):
        self._stitches.append(stitch)

    def remove_stitch(self, stitch):
        """Remove a stitch from the collection."""
        if stitch in self._stitches:
            self._stitches.remove(stitch)

    def get_stitches(self):
        return self._stitches

    def clear(self):
        self._stitches.clear()
        self.floss_image=processor.amended_image.copy()
        self.processor=processor        
        self.all_brands = brands
        self.project=project
        self.floss_image_dialog = FlossImageLabelDialog(self.floss_image, self)
        self.floss_image_dialog.show()
        self.bAmended=False
        print("In assignFlosses()")

        self.all_flosses = [] 
        for brand in allBrands.brands: 
            for floss in brand.flosses: 
                self.all_flosses.append(floss)

        central_widget = QWidget() 
        self.setCentralWidget(central_widget) 
        main_layout = QVBoxLayout()

        # Create table for image colors
        self.color_table = QTableWidget(0, 8)
        self.color_table.setHorizontalHeaderLabels(['RGB Value', '# Stitches', 'Colour', 'Floss', 'Floss Colour', 'Floss RGB', 'Floss Alternative Name', 'New Floss'])
        self.color_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        table_layout = QHBoxLayout()
        table_layout.addWidget(self.color_table)
        button_layout = QHBoxLayout()

        # Add save button
        buttonSave = QPushButton('Save and exit')
        buttonSave.clicked.connect(self.save_flosses)
        button_layout.addWidget(buttonSave)
   
        # Add exit button
        buttonExit = QPushButton('Exit')
        buttonExit.clicked.connect(self.exit_without_saving)
        button_layout.addWidget(buttonExit)
        
        # Get unique colors and their counts
        print("In AssignFlosses(). useColour: %d" % (self.project.use_colour))
        if self.project.use_colour:
            orig_colors, counts = np.unique(self.image.reshape(-1, self.image.shape[2]), axis=0, return_counts=True)
        
            for color, count in zip(orig_colors, counts): 
                self.add_color_row(color, count)
        else:
            self.add_color_row([0,0,0],1)
        self.brand_names = self.all_brands.getListOfBrandNames()
           

        main_layout.addLayout(table_layout)
        main_layout.addLayout(button_layout)
        
        central_widget.setLayout(main_layout)
        #self.adjustSize()

        #if self.project.useColour:
        self.match_flosses()
    def get_unique_colors(self, image):
        """Extract unique colors from the image"""
        # Reshape the image to a 2D array of pixels
        pixels = image.reshape(-1, 3)
        
        # Convert to a set of tuples for unique colors
        unique_colors = set(map(tuple, pixels))
        
        # Convert back to a list and sort for consistent display
        return sorted(list(unique_colors))
    def save_flosses(self):
        print("In assignFlossWindow.save_flosses()")

        # read each RGB, floss name and floss RGB from the table
        for row in range(self.color_table.rowCount()):
            itemName=self.color_table.item(row, 3)
            itemRGB=self.color_table.item(row, 0)
            itemFlossRGB=self.color_table.item(row, 5)
            flossName=itemName.text()
            origRGB=itemRGB.text()
            flossRGB=itemFlossRGB.text()
            #print("flossName: %s" % (flossName))

            # find the floss anf add it to the project
            newFloss=self.all_brands.findFlossFromName(flossName)
            self.project.addFloss(newFloss)
            self.project.dtFlossesAssigned=datetime.datetime.now()

        # Add the change to the undo list and then update the amended image
        
        self.processor.amended_image = self.floss_image.copy()
        self.processor.history.append(self.processor.amended_image.copy())
        self.processor.amended_image_colours=self.processor.get_number_of_colors(self.processor.amended_image)
        
        # Need to update the image labels in the main window.
        self.update_image_labels_signal.emit()
        self.result_signal.emit(True)

        # Close down the window
        self.floss_image_dialog.close()
        self.close()
        
    def refresh_colors(self, amended_image):
        """Refresh the floss assignments based on new image colors"""
        # Update the internal image reference
        self.amended_image = amended_image
        
        # Extract unique colors from the amended image
        self.unique_colors = self.get_unique_colors(amended_image)
        
        # Preserve existing assignments that are still valid
        new_color_floss_map = {}
        for color in self.unique_colors:
            if color in self.color_floss_map:
                new_color_floss_map[color] = self.color_floss_map[color]
        
        self.color_floss_map = new_color_floss_map
        
        # Update project's assigned flosses
        self.main_window.project.assigned_flosses = self.color_floss_map.copy()
        
        # Clear existing color widgets
        for i in reversed(range(self.colors_layout.count())): 
            self.colors_layout.itemAt(i).widget().setParent(None)
        
        # Recreate color widgets for new colors
        self.create_color_widgets()
        
        # Update the window and info pane
        self.update()
        self.main_window.update_info_pane()
    def exit_without_saving(self):
        # Close down the window
        self.result_signal.emit(False)
        self.floss_image_dialog.close()
        self.close()

    def closeEvent(self, event):        
        self.floss_image_dialog.close()
        event.accept()

    def open_filter_dialog(self, brands):
        dialog = FilterDialog(self, brands)
        if dialog.exec():
            checkedBrands = dialog.get_checked_brands()
            print('Checked items:', checkedBrands)
        dialog.exec()
        return checkedBrands

    def add_color_row(self, color, count):
        row_position = self.color_table.rowCount()
        self.color_table.insertRow(row_position)
        
        # RGB Value
        #rgb_value = f'{color[0]}, {color[1]}, {color[2]}'
        rgb_value = f'{color[2]}, {color[1]}, {color[0]}'
        self.color_table.setItem(row_position, 0, QTableWidgetItem(rgb_value))
        
        # Number of Stitches
        self.color_table.setItem(row_position, 1, QTableWidgetItem(str(count)))
        
        # Colour block
        color_label = QLabel()
        color_label.setFixedSize(50, 50)
        #color_label.setStyleSheet(f'background-color: rgb({color[0]}, {color[1]}, {color[2]});')
        color_label.setStyleSheet(f'background-color: rgb({color[2]}, {color[1]}, {color[0]});')
        self.color_table.setCellWidget(row_position, 2, color_label)
        
        # Floss field (editable)
        #floss_edit = QLineEdit()
        #floss_edit.editingFinished.connect(lambda f=floss_edit, r=row_position: self.validate_floss(f, r))
        #self.color_table.setCellWidget(row_position, 3, floss_edit)

        # Floss Alternative Name, Floss RGB, Floss Colour, Floss Name
        for col in range(3, 7):
            self.color_table.setItem(row_position, col, QTableWidgetItem(''))
        
        # Floss field (editable)
        floss_edit = QLineEdit()
        floss_edit.editingFinished.connect(lambda f=floss_edit, r=row_position: self.validate_floss(f, r))
        self.color_table.setCellWidget(row_position, 7, floss_edit)

        # Checkbox
        #checkbox = QCheckBox()
        #self.color_table.setCellWidget(row_position, 7, checkbox)

    def validate_floss(self, floss_edit, row_position):
        floss_name = floss_edit.text()
        matched_floss = next((floss for floss in self.all_flosses if floss.name == floss_name), None)

        if matched_floss:
            # First take the pre matched floss colour
            rgbOldFlossColour=list(map(int, self.color_table.item(row_position, 0).text().split(', ')))

            # Update table with floss info
            self.color_table.setItem(row_position, 6, QTableWidgetItem(matched_floss.color_name))
            self.color_table.setItem(row_position, 5, QTableWidgetItem(f'{matched_floss.rgb[0]}, {matched_floss.rgb[1]}, {matched_floss.rgb[2]}'))

            floss_color_label = QLabel()
            floss_color_label.setFixedSize(50, 50)
            floss_color_label.setStyleSheet(f'background-color: rgb({matched_floss.rgb[0]}, {matched_floss.rgb[1]}, {matched_floss.rgb[2]});')
            self.color_table.setCellWidget(row_position, 4, floss_color_label)

            self.color_table.setItem(row_position, 3, QTableWidgetItem(matched_floss.name))

            # Replace the image with the amended table to ensure that aeverything is correct
            self.floss_image=self.processor.amended_image.copy()
            # Update image with all the floss colors in the table
            for row in range(self.color_table.rowCount()):
                # Get the RGB value from the table
                item = self.color_table.item(row, 0)
                new_item = self.color_table.item(row, 5)

                if item:
                    rgb_value = list(map(int, item.text().split(', ')))
                    rgb_new_value = list(map(int, new_item.text().split(', ')))
                    #print("rgb_value: %s" % (str(rgb_value)))

                    #update the floss_image colour
                    if rgb_value != rgb_new_value:
                        self.processor.update_color(self.floss_image, rgb_value, rgb_new_value)
                    else:
                        print("Colour identical - not changed")

            #Update the label
            #rgb_value = list(map(int, self.color_table.item(row_position, 0).text().split(', ')))
            
            self.floss_image_dialog.set_image(self.floss_image)
  
    def create_floss_label(self, floss):
        #print("create_floss()")
        #print(floss.name, floss.color_name)
        #print("Name: %s    Alt-Name: %s" % (floss.name, floss.color_name))
        #floss_label = QLabel(f"{floss['name']}: {floss['alternative_name']}")
        floss_label = QLabel(f"{str(floss.name)}: {str(floss.color_name)}")
        floss_color = floss.rgb
        floss_label.setStyleSheet(f'background-color: rgb({floss_color[0]}, {floss_color[1]}, {floss_color[2]});')
        floss_label.setFixedSize(200, 50)
        return floss_label        

    def match_flosses(self):
        print("In assignFlossesWinow.match_flosses()")
        #print("# Rows in color_table: %d" % (range(self.color_table.rowCount())))
        for row in range(self.color_table.rowCount()):
            # Get the RGB value from the table
            item = self.color_table.item(row, 0)

            #if item: 
            #    print(f"Value of item at row {row}, column {0}: {item.text()}") 
            #else: 
            #    print(f"No item found at row {row}, column {0}")

            if item:
                rgb_value = list(map(int, item.text().split(', ')))
                #print("rgb_value: %s" % (str(rgb_value)))

                # Find the closest floss color
                closest_floss = self.find_closest_floss(rgb_value)
                #print("Orig RGB: %s   new RGB: %s    Floss Name: %s" % (str(rgb_value), str(closest_floss.rgb), closest_floss.name))
                # Populate the table with floss info
                self.color_table.setItem(row, 5, QTableWidgetItem(f'{closest_floss.rgb[0]}, {closest_floss.rgb[1]}, {closest_floss.rgb[2]}'))
                floss_color_label = QLabel()
                floss_color_label.setFixedSize(50, 50)
                floss_color_label.setStyleSheet(f'background-color: rgb({closest_floss.rgb[0]}, {closest_floss.rgb[1]}, {closest_floss.rgb[2]});')
                self.color_table.setCellWidget(row, 4, floss_color_label)
                self.color_table.setItem(row, 3, QTableWidgetItem(closest_floss.name))
                self.color_table.setItem(row, 6, QTableWidgetItem(closest_floss.color_name))

                #update the floss_image colour
                if rgb_value != closest_floss.rgb:
                    self.processor.update_color(self.floss_image, rgb_value, closest_floss.rgb)
                else:
                    print("Colour identical - not changed")

        # update the floss_image_label
        self.floss_image_dialog.set_image(self.floss_image)
        print("Should have updated the floss label")

    def find_closest_floss(self, target_rgb):
        min_distance = float('inf')
        closest_floss = None
        #print(self.allFlosses)
        
        for floss in self.all_flosses:
            distance = np.linalg.norm(np.array(target_rgb) - np.array(floss.rgb))
            if distance < min_distance:
                min_distance = distance
                closest_floss = floss
        
        return closest_floss
    def assign_floss(self, rgb_color, floss):
        """Assign a floss to a specific RGB color"""
        # Update the assignment in the window
        self.color_floss_map[rgb_color] = floss
        
        # Update the project's floss assignments
        self.main_window.project.assigned_flosses[rgb_color] = floss
        
        # Update the info pane in the main window
        self.main_window.update_info_pane()
class EditImageWindow(QWidget):
    update_image_labels_signal = Signal()
    result_signal = Signal(bool)

    def __init__(self, processor, parent=None):
        #super().__init__()
        super().__init__(parent)
        self.setWindowTitle("Edit Image")
        self.setGeometry(100, 100, 600, 800)
        self.processor=processor
        self.bgrEditImage=self.processor.amended_image.copy()
        self.bAmended=False
        
        # Layout time
        layout = QVBoxLayout()

        # Image box
        self.edit_image_label = QLabel("No Image Opened")
        self.edit_image_label.setAlignment(Qt.AlignCenter)
        self.edit_image_label.setStyleSheet("background-color: lightgray; border: 1px solid black;")
        self.edit_image_label.setFixedSize(300,300)
        self.set_image(self.bgrEditImage, self.edit_image_label)

        # Layout for Controls
        control_layout = QGridLayout()
        layout.addWidget(self.edit_image_label)

        # Gamma controls
        self.gamma_label = QLabel('Gamma')
        self.gamma_slider = QSlider(Qt.Horizontal)
        self.gamma_slider.setMinimum(10)
        self.gamma_slider.setMaximum(300)
        self.gamma_slider.setValue(100)
        self.gamma_slider.valueChanged.connect(self.adjust_image)

        # Vibrancy controls
        self.vibrancy_label = QLabel('Vibrancy')
        self.vibrancy_slider = QSlider(Qt.Horizontal)
        self.vibrancy_slider.setMinimum(0)
        self.vibrancy_slider.setMaximum(200)
        self.vibrancy_slider.setValue(100)
        self.vibrancy_slider.valueChanged.connect(self.adjust_image)        

        self.reset_button = QPushButton("Reset", self)
        self.reset_button.clicked.connect(self.reset)

        self.confirm_button = QPushButton("Confirm", self)
        self.confirm_button.clicked.connect(self.confirm)

        self.cancel_button = QPushButton("Cancel", self)
        self.cancel_button.clicked.connect(self.cancel)

        #control_layout.addWidget((self.color_input),0,0)
        #control_layout.addWidget((self.reduce_button),0,1)
        control_layout.addWidget((self.gamma_label),1,0)
        control_layout.addWidget((self.gamma_slider),1,1)
        control_layout.addWidget((self.vibrancy_label),1,2)
        control_layout.addWidget((self.vibrancy_slider),1,3)
        control_layout.addWidget((self.reset_button),2,0)
        control_layout.addWidget((self.confirm_button),2,1)
        control_layout.addWidget((self.cancel_button),2,2)
        layout.addLayout(control_layout)

        self.setLayout(layout)


    def adjust_image(self):
        #size_value = self.size_slider.value()
        gamma_value = self.gamma_slider.value() / 100.0
        vibrancy_value = self.vibrancy_slider.value() / 100.0
        temp_image=self.processor.amended_image
        
        # Resize the image
        #new_size = (int(self.bgrEditImage.shape[1] * size_value / 100), int(self.bgrEditImage.shape[0] * size_value / 100))
        #resized_image = cv2.resize(self.bgrEditImage, new_size)

        # Adjust gamma
        #gamma_corrected = np.array(255 * (resized_image / 255) ** gamma_value, dtype='uint8')
        gamma_corrected = np.array(255 * (temp_image / 255) ** gamma_value, dtype='uint8')

        # Adjust vibrancy (simple saturation adjustment)
        hsv_image = cv2.cvtColor(gamma_corrected, cv2.COLOR_RGB2HSV)
        hsv_image[:, :, 1] = np.clip(hsv_image[:, :, 1] * vibrancy_value, 0, 255)
        adjusted_image = cv2.cvtColor(hsv_image, cv2.COLOR_HSV2RGB)
        

        self.adjusted_image = adjusted_image
        #self.image_updated.emit(self.adjusted_image)
        self.bgrEditImage=adjusted_image
        self.update_image_label()


    def reset(self):
        print("In EditImageWindow.reset()")
        # reset the local image to the amended image
        self.bgrEditImage=self.processor.amended_image.copy()
        self.gamma_slider.setValue(100)
        self.vibrancy_slider.setValue(100)

        self.update_image_label()

    def confirm(self):
        print("In EditImageWindow.confirm()")
        # save the local image to the Amended image and update the label - then exit
        self.processor.amended_image = self.bgrEditImage.copy()
        
        # Update add to the undo list
        self.processor.history.append(self.processor.amended_image)
        #self.processor.future.clear()
        self.update_image_labels_signal.emit()
        self.result_signal.emit(True)
        self.close()

    def cancel(self):
        print("In EditImageWindow.cancel()")
        self.bgrEditImage=self.processor.amended_image.copy()
        self.result_signal.emit(False)
        self.close()

    def update_image_label(self):
        #print("In EditMainWindow.update_image_labels()")
        
        #self.amendedImageColours=self.processor.get_number_of_colors(self.processor.amended_image)
    
        self.set_image(self.bgrEditImage, self.edit_image_label)
        #self.labelEditColours.setText(f"Editted Image Colours: {self.dEditImageColours}")
        #self.color_input.setValidator(QIntValidator(1, self.processor.amended_image_colours))
        #self.color_input.setPlaceholderText(f"Enter number of colors (1-{self.processor.amended_image_colours})")
        #print("Orig # Colours: %d   Amended # Colours: %d" % (self.processor.original_image_colours, self.processor.amended_image_colours))

    def set_image(self, image, label):
        #print("In EditImageWindow.set_image()")
        height, width, channel = image.shape
        bytes_per_line = 3 * width
        q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_BGR888)
        pixmap = QPixmap.fromImage(q_image)
        label.setPixmap(pixmap.scaled(label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
   
class ViewFlossesWindow(QWidget):

    def __init__(self, brands, view_flosses_action):
        super().__init__() 
        self.view_flosses_action = view_flosses_action  # Store reference to action
        self.setWindowTitle("Available Flosses") 
        self.setGeometry(200, 200, 220, 700)  
        self.all_brands = brands

        print("In ViewFlossesWindow()")

        self.all_flosses = [] 
        for brand in allBrands.brands: 
            for floss in brand.flosses: 
                self.all_flosses.append(floss)

        #central_widget = QWidget() 
        #self.setCentralWidget(central_widget) 
        main_layout = QVBoxLayout()
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        form_layout = QFormLayout()

        # Create scrollable list for flosses
        #floss_widget = QWidget()
        #floss_layout = QVBoxLayout()

        for currentBrand in self.all_brands.brands:
            for currentFloss in currentBrand.flosses:
                form_layout.addRow(self.create_floss_label(currentFloss))
      
        #floss_widget.setLayout(floss_layout)
        
        scroll_content=QWidget()
        scroll_content.setLayout(form_layout)
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
        
        #central_widget.setLayout(main_layout)
        self.setLayout(main_layout)

    def create_floss_label(self, floss):
        #print("create_floss()")
        #print(floss.name, floss.color_name)
        #print("Name: %s    Alt-Name: %s" % (floss.name, floss.color_name))
        #floss_label = QLabel(f"{floss['name']}: {floss['alternative_name']}")
        floss_label = QLabel(f"{str(floss.name)}: {str(floss.color_name)}")
        floss_color = floss.rgb
        floss_label.setStyleSheet(f'background-color: rgb({floss_color[0]}, {floss_color[1]}, {floss_color[2]});')
        floss_label.setFixedSize(200, 50)
        return floss_label        


    def closeEvent(self, event):
        """Clean up when window is closed"""
        self._view_flosses_window = None
        event.accept()

class FilterDialog(QDialog):
    def __init__(self, brands, parent=None):
        super().__init__(parent)
        self.setWindowTitle('Choose which Brands to include')
        self.brands = brands
        self.checkedBrands = []

        layout = QVBoxLayout()
        
        for brand in self.brands: 
            checkbox = QCheckBox(brand) 
            layout.addWidget(self.checkbox)

        
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.acceptBrand)
        button_box.rejected.connect(self.rejectBrand)
        layout.addWidget(button_box)
        self.setLayout(layout)


    def acceptBrand(self):
        self.checkedBrands = [cb.text() for cb in self.checkboxes if cb.isChecked()]
        super().accept()

    def get_checked_brands(self):
        return self.checkedBrands

class MotifInfoPane(QWidget):
    nameChanged = Signal(str)  # Add signal
    
    def __init__(self, motif):
        super().__init__()
        self.motif = motif
        self.layout = QVBoxLayout()
        
        # Add name edit field
        self.name_edit = QLineEdit(self.motif.name)
        self.name_edit.setPlaceholderText("Enter Motif Name")
        self.name_edit.textChanged.connect(self._on_name_changed)
        
        # Add to layout first
        self.layout.addWidget(self.name_edit)
        
        # Create labels
        self.total_layers_label = QLabel("Total Layers: 1")
        self.current_layer_label = QLabel("Current Layer: 1")
        self.motif_size_label = QLabel("Motif Size: 0 x 0")
        #self.stack_coverage_label = QLabel("Stack Coverage: 0% - 0%")

        # Add to layout
        self.layout.addWidget(self.total_layers_label)
        self.layout.addWidget(self.current_layer_label)
        self.layout.addWidget(self.motif_size_label)
        #self.layout.addWidget(self.stack_coverage_label)
        self.setLayout(self.layout)
        
    def _on_name_changed(self, text):
        self.motif.name = text
        self.nameChanged.emit(text)
        
    def update_info(self, total_layers, current_layer, sizeX, sizeY):
        self.total_layers_label.setText(f"Total Layers: {total_layers}")
        self.current_layer_label.setText(f"Current Layer: {current_layer}")
        cellsWide=int(sizeX/40)
        cellsHigh=int(sizeY/40)
        self.motif_size_label.setText(f"Motif Size: {cellsWide} x {cellsHigh}")


    def setup_layer_controls(self):
        # Create controls container
        self.controls = QWidget()
        layout = QHBoxLayout()
        
        # Layer navigation
        self.layer_label = QLabel(f"Layer {self.current_layer.number}")
        self.prev_layer_btn = QPushButton("Previous Layer")
        self.next_layer_btn = QPushButton("Next Layer")
        self.add_layer_btn = QPushButton("Add Layer")
        self.delete_layer_btn = QPushButton("Delete Layer")
        
        # Add widgets to layout
        layout.addWidget(self.layer_label)
        layout.addWidget(self.prev_layer_btn)
        layout.addWidget(self.next_layer_btn)
        layout.addWidget(self.add_layer_btn)
        layout.addWidget(self.delete_layer_btn)
        
        # Connect signals
        self.prev_layer_btn.clicked.connect(self.previous_layer)
        self.next_layer_btn.clicked.connect(self.next_layer)
        self.add_layer_btn.clicked.connect(self.add_layer)
        self.delete_layer_btn.clicked.connect(self.delete_layer)
        
        self.controls.setLayout(layout)

    @property
    def current_layer(self):
        return self.layers[self.current_layer_index]

    def add_layer(self):
        new_layer = self.current_layer.copy()
        self.layers.insert(self.current_layer_index + 1, new_layer)
        self.current_layer_index += 1
        self.refresh_display()
        #self.info_pane.update_info(len(self.layers), self.current_layer.number)

    def delete_layer(self):
        if len(self.layers) > 1:
            reply = QMessageBox.question(self, 'Delete Layer', 
                                       'Are you sure you want to delete this layer?',
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.layers.pop(self.current_layer_index)
                if self.current_layer_index >= len(self.layers):
                    self.current_layer_index -= 1
                self.refresh_display()
                #self.info_pane.update_info(len(self.layers), self.current_layer.number)

    def previous_layer(self):
        if self.current_layer_index > 0:
            self.current_layer_index -= 1
            self.refresh_display()
            #self.info_pane.update_info(len(self.layers), self.current_layer.number)

    def next_layer(self):
        if self.current_layer_index < len(self.layers) - 1:
            self.current_layer_index += 1
            self.refresh_display()
            #self.info_pane.update_info(len(self.layers), self.current_layer.number)

    def refresh_display(self):
        # Clear existing lines
        for item in self.scene.items():
            if isinstance(item, QGraphicsLineItem):
                self.scene.removeItem(item)
       
        self.init_grid()

        # Redraw current layer
        for stitch in self.current_layer.stitches:
            pen = QPen(Qt.black, stitch.weight)
            self.scene.addLine(QLineF(stitch.stitchStart, stitch.stitchEnd), pen)
        
        # Update layer label
        self.layer_label.setText(f"Layer {self.current_layer.number}")
        self.update_stitch_display()
        minX, minY, maxX, maxY = self.findExtentsAcrossAllLayers()
        sizeX, sizeY = maxX - minX, maxY - minY
        self.info_pane.update_info(len(self.layers), self.current_layer.number, sizeX, sizeY)

        # Draw Motif Boundary
        self.drawMotifBoundary(minX, minY, maxX, maxY)

    def drawMotifBoundary(self, minX, minY, maxX, maxY):
        pen = QPen(Qt.red)
        self.scene.addLine(QLineF(minX, minY, maxX, minY), pen)
        self.scene.addLine(QLineF(minX, minY, minX, maxY), pen)
        self.scene.addLine(QLineF(minX, maxY, maxX, maxY), pen)
        self.scene.addLine(QLineF(maxX, minY, maxX, maxY), pen)

    def has_duplicate_stitch(self, new_stitch):
        for i, existing_stitch in enumerate(self.current_layer.stitches):
            # Check both directions
            if ((abs(existing_stitch.stitchStart.x() - new_stitch.stitchStart.x()) < 1 and
                 abs(existing_stitch.stitchStart.y() - new_stitch.stitchStart.y()) < 1 and
                 abs(existing_stitch.stitchEnd.x() - new_stitch.stitchEnd.x()) < 1 and
                 abs(existing_stitch.stitchEnd.y() - new_stitch.stitchEnd.y()) < 1) or
                (abs(existing_stitch.stitchStart.x() - new_stitch.stitchEnd.x()) < 1 and
                 abs(existing_stitch.stitchStart.y() - new_stitch.stitchEnd.y()) < 1 and
                 abs(existing_stitch.stitchEnd.x() - new_stitch.stitchStart.x()) < 1 and
                 abs(existing_stitch.stitchEnd.y() - new_stitch.stitchStart.y()) < 1)):
                return i
        return -1
    
    def update_stitch_display(self):
        display_text = f"Layer {self.current_layer.number} Stitches:\n\n"
        for i, stitch in enumerate(self.current_layer.stitches, 1):
            display_text += f"Stitch {i}:\n"
            display_text += f"Start: ({stitch.stitchStart.x()}, {stitch.stitchStart.y()})\n"
            display_text += f"End: ({stitch.stitchEnd.x()}, {stitch.stitchEnd.y()})\n"
            display_text += f"Weight: {stitch.weight}\n\n"
        print(display_text)


    def init_grid(self):
        pen = QPen(Qt.black)
        for x in range(self.grid_size + 1):
            xpos = x * self.cell_size
            self.scene.addLine(QLineF(xpos, 0, xpos, self.grid_size * self.cell_size), pen)
            self.scene.addLine(QLineF(0, xpos, self.grid_size * self.cell_size, xpos), pen)

    def toggle_weight(self):
        self.weight = 2 if self.weight == 4 else 4

    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.RightButton:
            # Get exact mouse position in scene coordinates
            mouse_pos = self.mapToScene(event.position().toPoint())
            
            # Find the closest stitch to remove
            min_distance = float('inf')
            stitch_to_remove = None
            
            for stitch in self.motif.current_layer.stitches:
                # Calculate distances to the line segment
                p1 = stitch.stitchStart
                p2 = stitch.stitchEnd
                
                # Calculate closest point on line segment to mouse click
                line_vec = QPointF(p2.x() - p1.x(), p2.y() - p1.y())
                point_vec = QPointF(mouse_pos.x() - p1.x(), mouse_pos.y() - p1.y())
                
                line_length_sq = line_vec.x()**2 + line_vec.y()**2
                
                if line_length_sq == 0:
                    # If start and end points are the same, just use distance to that point
                    dist = ((mouse_pos.x() - p1.x())**2 + (mouse_pos.y() - p1.y())**2)**0.5
                else:
                    # Project mouse point onto line segment
                    t = max(0, min(1, (point_vec.x() * line_vec.x() + point_vec.y() * line_vec.y()) / line_length_sq))
                    
                    # Calculate closest point on line
                    closest_x = p1.x() + t * line_vec.x()
                    closest_y = p1.y() + t * line_vec.y()
                    
                    # Calculate distance from mouse to closest point
                    dist = ((mouse_pos.x() - closest_x)**2 + (mouse_pos.y() - closest_y)**2)**0.5
                
                if dist < min_distance:
                    min_distance = dist
                    stitch_to_remove = stitch
            
            # Remove only if we found a stitch close enough
            if stitch_to_remove and min_distance < self.cell_size/2:
                self.motif.current_layer.stitches.remove(stitch_to_remove)
                # Remove the visual line
                for item in self.scene.items():
                    if isinstance(item, QGraphicsLineItem):
                        line = item.line()
                        if (abs(line.p1().x() - stitch_to_remove.stitchStart.x()) < 1 and 
                            abs(line.p1().y() - stitch_to_remove.stitchStart.y()) < 1 and
                            abs(line.p2().x() - stitch_to_remove.stitchEnd.x()) < 1 and 
                            abs(line.p2().y() - stitch_to_remove.stitchEnd.y()) < 1):
                            self.scene.removeItem(item)
                            break
                self.refresh_display()
        
        elif event.button() == Qt.LeftButton:
            pos = self.mapToScene(event.position().toPoint())
            x = round(pos.x() / self.cell_size) * self.cell_size
            y = round(pos.y() / self.cell_size) * self.cell_size
            self.start_point = self.snap_to_grid(QPointF(x, y))
            if self.temp_line is not None:
                self.scene.removeItem(self.temp_line)
                self.temp_line = None

    def mouseMoveEvent(self, event):
        if self.start_point is not None:
            pos = self.mapToScene(event.position().toPoint())
            # Snap to grid but allow diagonal movement
            end_point = self.snap_to_grid(pos)
            
            # Create or update temp line
            if self.temp_line is None:
                pen = QPen(Qt.red, self.weight)
                self.temp_line = self.scene.addLine(QLineF(self.start_point, end_point), pen)
            else:
                self.temp_line.setLine(QLineF(self.start_point, end_point))

    def is_valid_stitch(self, start: QPointF, end: QPointF) -> bool:
        # Convert to grid units
        start_x = int(start.x() / self.cell_size)
        start_y = int(start.y() / self.cell_size)
        end_x = int(end.x() / self.cell_size)
        end_y = int(end.y() / self.cell_size)
        
        # Check if points are adjacent (including diagonals)
        dx = abs(end_x - start_x)
        dy = abs(end_y - start_y)
        
        # Allow only moves to adjacent points (including diagonals)
        return dx <= 1 and dy <= 1 and not (dx == 0 and dy == 0)  
    
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton and self.start_point is not None:
            pos = self.mapToScene(event.position().toPoint())
            end_point = self.snap_to_grid(pos)
            
            if self.is_valid_grid_point(end_point) and self.is_valid_stitch(self.start_point, end_point):
                new_stitch = Stitch(self.start_point, end_point, self.weight)
                
                # Check for duplicate stitches first
                duplicate_index = self.has_duplicate_stitch(new_stitch)
                if duplicate_index >= 0:
                    # Remove the existing stitch from the layer's stitches list
                    self.motif.current_layer.stitches.pop(duplicate_index)
                
                # Add the new stitch to the layer's stitches list
                self.motif.current_layer.stitches.append(new_stitch)
                
                # Update visual representation
                self.refresh_display()
            
            # Clean up temporary line safely
            if self.temp_line is not None:
                if self.temp_line.scene() == self.scene:
                    self.scene.removeItem(self.temp_line)
                self.temp_line = None
            self.start_point = None

    def snap_to_grid(self, pos):
        x = round(pos.x() / self.cell_size) * self.cell_size
        y = round(pos.y() / self.cell_size) * self.cell_size
        return QPointF(x, y)

    def is_adjacent(self, p1, p2):
        return (abs(p1.x() - p2.x()) == self.cell_size) ^ (abs(p1.y() - p2.y()) == self.cell_size)

    def find_stitch(self, x, y):
        for stitch in self.current_layer.stitches:
            if stitch.line().p1() == self.snap_to_grid(QPointF(x, y)) or stitch.line().p2() == self.snap_to_grid(QPointF(x,y)):
                return stitch
        return None

    def is_valid_grid_point(self, point):
        x = point.x()
        y = point.y()
        return (x % self.cell_size == 0 and 
                y % self.cell_size == 0 and
                0 <= x <= self.grid_size * self.cell_size and
                0 <= y <= self.grid_size * self.cell_size)

    def findLayerExtents(self):
        minX = self.grid_size
        minY = self.grid_size
        maxX = 0
        maxY = 0
        for stitch in self.current_layer.stitches:
            minX = min(minX, stitch.stitchStart.x(), stitch.stitchEnd.x())
            minY = min(minY, stitch.stitchStart.y(), stitch.stitchEnd.y())
            maxX = max(maxX, stitch.stitchStart.x(), stitch.stitchEnd.x())
            maxY = max(maxY, stitch.stitchStart.y(), stitch.stitchEnd.y())
        return minX, minY, maxX, maxY
    
    def findExtentsAcrossAllLayers(self):
        minX = self.grid_size*40
        minY = self.grid_size*40
        maxX = 0
        maxY = 0
        for layer in self.layers:
            for stitch in layer.stitches:
                #print(stitch)
                minX = min(minX, stitch.stitchStart.x(), stitch.stitchEnd.x())
                minY = min(minY, stitch.stitchStart.y(), stitch.stitchEnd.y())
                maxX = max(maxX, stitch.stitchStart.x(), stitch.stitchEnd.x())
                maxY = max(maxY, stitch.stitchStart.y(), stitch.stitchEnd.y())
            print("minX: %d, minY: %d, maxX: %d, maxY: %d" % (minX, minY, maxX, maxY))
            print("sizeX: %d, sizeY: %d" % ((maxX/40) - (minX/40), (maxY/40) - (minY/40)))
        return minX, minY, maxX, maxY

class MotifGrid(QGraphicsView):
    def __init__(self, info_pane, motif):
        super().__init__()
        self.info_pane = info_pane  # Store reference to InfoPane
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        self.grid_size = 10
        self.cell_size = 40
        self.weight = 2
        self.motif = motif
        #self.current_layer_index = 0
        self.init_grid()
        self.start_point = None
        self.temp_line = None
        self.setup_layer_controls()
        self.show_boundary = True  # Default to showing boundary
        
        # Remove layout setup from here since it's managed by MainWindow

    def setup_layer_controls(self):
        # Create controls container
        self.controls = QWidget()
        layout = QHBoxLayout()
        
        # Layer navigation
        #self.layer_label = QLabel(f"Layer {self.current_layer.number}")
        self.layer_label = QLabel(f"Layer {self.motif.current_layer.number}")
        self.prev_layer_btn = QPushButton("Previous Layer")
        self.next_layer_btn = QPushButton("Next Layer")
        self.add_layer_btn = QPushButton("Add Layer")
        self.delete_layer_btn = QPushButton("Delete Layer")
        
        # Add widgets to layout
        layout.addWidget(self.layer_label)
        layout.addWidget(self.prev_layer_btn)
        layout.addWidget(self.next_layer_btn)
        layout.addWidget(self.add_layer_btn)
        layout.addWidget(self.delete_layer_btn)
        
        # Connect signals
        self.prev_layer_btn.clicked.connect(self.previous_layer)
        self.next_layer_btn.clicked.connect(self.next_layer)
        self.add_layer_btn.clicked.connect(self.add_layer)
        self.delete_layer_btn.clicked.connect(self.delete_layer)
        
        self.controls.setLayout(layout)

    @property
    def current_layer(self):
        #return self.layers[self.current_layer_index]
        return self.motif.layers[self.motif.current_layer_index]

    def add_layer(self):
        #new_layer = self.current_layer.copy()
        #self.layers.insert(self.current_layer_index + 1, new_layer)
        #self.current_layer_index += 1
        self.motif.add_layer()
        self.refresh_display()
        #self.info_pane.update_info(len(self.layers), self.current_layer.number)

    def delete_layer(self):
        #if len(self.layers) > 1:
        #    reply = QMessageBox.question(self, 'Delete Layer', 
        #                               'Are you sure you want to delete this layer?',
        #                               QMessageBox.Yes | QMessageBox.No)
        #    if reply == QMessageBox.Yes:
        #        self.layers.pop(self.current_layer_index)
        #        if self.current_layer_index >= len(self.layers):
        #            self.current_layer_index -= 1
        if self.motif.delete_layer():
            self.refresh_display()
                #self.info_pane.update_info(len(self.layers), self.current_layer.number)

    def previous_layer(self):
        #if self.current_layer_index > 0:
        if self.motif.current_layer_index > 0:
            self.motif.current_layer_index -= 1
            self.refresh_display()
            #self.info_pane.update_info(len(self.layers), self.current_layer.number)

    def next_layer(self):
        if self.motif.current_layer_index < len(self.motif.layers) - 1:
            self.motif.current_layer_index += 1
            self.refresh_display()
            #self.info_pane.update_info(len(self.layers), self.current_layer.number)

    def refresh_display(self):
        # Clear existing lines
        for item in self.scene.items():
            if isinstance(item, QGraphicsLineItem):
                self.scene.removeItem(item)
       
        self.init_grid()

        # Redraw current layer
        for stitch in self.current_layer.stitches:
            pen = QPen(Qt.black, stitch.weight)
            self.scene.addLine(QLineF(stitch.stitchStart, stitch.stitchEnd), pen)
        
        # Update layer label
        #self.layer_label.setText(f"Layer {self.current_layer.number}")
        self.layer_label.setText(f"Layer {self.motif.current_layer.number}")
        #self.update_stitch_display()
        minX, minY, maxX, maxY = self.findExtentsAcrossAllLayers()
        sizeX, sizeY = maxX - minX, maxY - minY
        self.motif.tilesWide=int(sizeX/40)
        self.motif.tilesHigh=int(sizeY/40)

        #self.info_pane.update_info(len(self.layers), self.current_layer.number, sizeX, sizeY)
        self.info_pane.update_info(len(self.motif.layers), self.motif.current_layer.number, sizeX, sizeY)   

        # Draw Motif Boundary
        self.drawMotifBoundary(minX, minY, maxX, maxY)

    def drawMotifBoundary(self, minX, minY, maxX, maxY):
        if not self.show_boundary:
            return
            
        pen = QPen(Qt.red)
        pen.setStyle(Qt.DashLine)
        self.scene.addLine(QLineF(minX, minY, maxX, minY), pen)
        self.scene.addLine(QLineF(minX, minY, minX, maxY), pen)
        self.scene.addLine(QLineF(minX, maxY, maxX, maxY), pen)
        self.scene.addLine(QLineF(maxX, minY, maxX, maxY), pen)

    def has_duplicate_stitch(self, new_stitch):
        #for i, existing_stitch in enumerate(self.current_layer.stitches):
        for i, existing_stitch in enumerate(self.motif.current_layer.stitches):
            # Check both directions
            if ((abs(existing_stitch.stitchStart.x() - new_stitch.stitchStart.x()) < 1 and
                 abs(existing_stitch.stitchStart.y() - new_stitch.stitchStart.y()) < 1 and
                 abs(existing_stitch.stitchEnd.x() - new_stitch.stitchEnd.x()) < 1 and
                 abs(existing_stitch.stitchEnd.y() - new_stitch.stitchEnd.y()) < 1) or
                (abs(existing_stitch.stitchStart.x() - new_stitch.stitchEnd.x()) < 1 and
                 abs(existing_stitch.stitchStart.y() - new_stitch.stitchEnd.y()) < 1 and
                 abs(existing_stitch.stitchEnd.x() - new_stitch.stitchStart.x()) < 1 and
                 abs(existing_stitch.stitchEnd.y() - new_stitch.stitchStart.y()) < 1)):
                return i
        return -1
    
    def update_stitch_display(self):
        #display_text = f"Layer {self.current_layer.number} Stitches:\n\n"
        display_text = f"Layer {self.motif.current_layer.number} Stitches:\n\n"
        #for i, stitch in enumerate(self.current_layer.stitches, 1):
        for i, stitch in enumerate(self.motif.current_layer.stitches, 1):
            display_text += f"Stitch {i}:\n"
            display_text += f"Start: ({stitch.stitchStart.x()}, {stitch.stitchStart.y()})\n"
            display_text += f"End: ({stitch.stitchEnd.x()}, {stitch.stitchEnd.y()})\n"
            display_text += f"Weight: {stitch.weight}\n\n"
        print(display_text)


    def init_grid(self):
        pen = QPen(Qt.black)
        for x in range(self.grid_size + 1):
            xpos = x * self.cell_size
            self.scene.addLine(QLineF(xpos, 0, xpos, self.grid_size * self.cell_size), pen)
            self.scene.addLine(QLineF(0, xpos, self.grid_size * self.cell_size, xpos), pen)

    #def toggle_weight(self):
    #    self.weight = 2 if self.weight == 4 else 4

    def toggle_boundary(self):
        self.show_boundary = not self.show_boundary

    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.RightButton:
            # Get exact mouse position in scene coordinates
            mouse_pos = self.mapToScene(event.position().toPoint())
            
            # Find the closest stitch to remove
            min_distance = float('inf')
            stitch_to_remove = None
            
            for stitch in self.motif.current_layer.stitches:
                # Calculate distances to the line segment
                p1 = stitch.stitchStart
                p2 = stitch.stitchEnd
                
                # Calculate closest point on line segment to mouse click
                line_vec = QPointF(p2.x() - p1.x(), p2.y() - p1.y())
                point_vec = QPointF(mouse_pos.x() - p1.x(), mouse_pos.y() - p1.y())
                
                line_length_sq = line_vec.x()**2 + line_vec.y()**2
                
                if line_length_sq == 0:
                    # If start and end points are the same, just use distance to that point
                    dist = ((mouse_pos.x() - p1.x())**2 + (mouse_pos.y() - p1.y())**2)**0.5
                else:
                    # Project mouse point onto line segment
                    t = max(0, min(1, (point_vec.x() * line_vec.x() + point_vec.y() * line_vec.y()) / line_length_sq))
                    
                    # Calculate closest point on line
                    closest_x = p1.x() + t * line_vec.x()
                    closest_y = p1.y() + t * line_vec.y()
                    
                    # Calculate distance from mouse to closest point
                    dist = ((mouse_pos.x() - closest_x)**2 + (mouse_pos.y() - closest_y)**2)**0.5
                
                if dist < min_distance:
                    min_distance = dist
                    stitch_to_remove = stitch
            
            # Remove only if we found a stitch close enough
            if stitch_to_remove and min_distance < self.cell_size/2:
                self.motif.current_layer.stitches.remove(stitch_to_remove)
                # Remove the visual line
                for item in self.scene.items():
                    if isinstance(item, QGraphicsLineItem):
                        line = item.line()
                        if (abs(line.p1().x() - stitch_to_remove.stitchStart.x()) < 1 and 
                            abs(line.p1().y() - stitch_to_remove.stitchStart.y()) < 1 and
                            abs(line.p2().x() - stitch_to_remove.stitchEnd.x()) < 1 and 
                            abs(line.p2().y() - stitch_to_remove.stitchEnd.y()) < 1):
                            self.scene.removeItem(item)
                            break
                self.refresh_display()
        
        elif event.button() == Qt.LeftButton:
            pos = self.mapToScene(event.position().toPoint())
            x = round(pos.x() / self.cell_size) * self.cell_size
            y = round(pos.y() / self.cell_size) * self.cell_size
            self.start_point = self.snap_to_grid(QPointF(x, y))
            if self.temp_line is not None:
                self.scene.removeItem(self.temp_line)
                self.temp_line = None

    def mouseMoveEvent(self, event):
        if self.start_point is not None:
            pos = self.mapToScene(event.position().toPoint())
            # Snap to grid but allow diagonal movement
            end_point = self.snap_to_grid(pos)
            
            # Create or update temp line
            if self.temp_line is None:
                pen = QPen(Qt.red, self.weight)
                self.temp_line = self.scene.addLine(QLineF(self.start_point, end_point), pen)
            else:
                self.temp_line.setLine(QLineF(self.start_point, end_point))

    def is_valid_stitch(self, start: QPointF, end: QPointF) -> bool:
        # Convert to grid units
        start_x = int(start.x() / self.cell_size)
        start_y = int(start.y() / self.cell_size)
        end_x = int(end.x() / self.cell_size)
        end_y = int(end.y() / self.cell_size)
        
        # Check if points are adjacent (including diagonals)
        dx = abs(end_x - start_x)
        dy = abs(end_y - start_y)
        
        # Allow only moves to adjacent points (including diagonals)
        return dx <= 1 and dy <= 1 and not (dx == 0 and dy == 0)  
    
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton and self.start_point is not None:
            pos = self.mapToScene(event.position().toPoint())
            end_point = self.snap_to_grid(pos)
            
            if self.is_valid_grid_point(end_point) and self.is_valid_stitch(self.start_point, end_point):
                new_stitch = Stitch(self.start_point, end_point, self.weight)
                
                # Check for duplicate stitches first
                duplicate_index = self.has_duplicate_stitch(new_stitch)
                if duplicate_index >= 0:
                    # Remove the existing stitch from the layer's stitches list
                    self.motif.current_layer.stitches.pop(duplicate_index)
                
                # Add the new stitch to the layer's stitches list
                self.motif.current_layer.stitches.append(new_stitch)
                
                # Update visual representation
                self.refresh_display()
            
            # Clean up temporary line safely
            if self.temp_line is not None:
                if self.temp_line.scene() == self.scene:
                    self.scene.removeItem(self.temp_line)
                self.temp_line = None
            self.start_point = None

    def snap_to_grid(self, pos):
        x = round(pos.x() / self.cell_size) * self.cell_size
        y = round(pos.y() / self.cell_size) * self.cell_size
        return QPointF(x, y)

    def is_adjacent(self, p1, p2):
        return (abs(p1.x() - p2.x()) == self.cell_size) ^ (abs(p1.y() - p2.y()) == self.cell_size)

    def find_stitch(self, x, y):
        for stitch in self.current_layer.stitches:
            if stitch.line().p1() == self.snap_to_grid(QPointF(x, y)) or stitch.line().p2() == self.snap_to_grid(QPointF(x,y)):
                return stitch
        return None

    def is_valid_grid_point(self, point):
        x = point.x()
        y = point.y()
        return (x % self.cell_size == 0 and 
                y % self.cell_size == 0 and
                0 <= x <= self.grid_size * self.cell_size and
                0 <= y <= self.grid_size * self.cell_size)

    def findLayerExtents(self):
        minX = self.grid_size
        minY = self.grid_size
        maxX = 0
        maxY = 0
        #for stitch in self.current_layer.stitches:
        for stitch in self.motif.current_layer.stitches:
            minX = min(minX, stitch.stitchStart.x(), stitch.stitchEnd.x())
            minY = min(minY, stitch.stitchStart.y(), stitch.stitchEnd.y())
            maxX = max(maxX, stitch.stitchStart.x(), stitch.stitchEnd.x())
            maxY = max(maxY, stitch.stitchStart.y(), stitch.stitchEnd.y())
        return minX, minY, maxX, maxY
    
    def findExtentsAcrossAllLayers(self):
        minX = self.grid_size*40
        minY = self.grid_size*40
        maxX = 0
        maxY = 0
        #for layer in self.layers:
        for layer in self.motif.layers:
            for stitch in layer.stitches:
                #print(stitch)
                minX = min(minX, stitch.stitchStart.x(), stitch.stitchEnd.x())
                minY = min(minY, stitch.stitchStart.y(), stitch.stitchEnd.y())
                maxX = max(maxX, stitch.stitchStart.x(), stitch.stitchEnd.x())
                maxY = max(maxY, stitch.stitchStart.y(), stitch.stitchEnd.y())
            #print("minX: %d, minY: %d, maxX: %d, maxY: %d" % (minX, minY, maxX, maxY))
            #print("sizeX: %d, sizeY: %d" % ((maxX/40) - (minX/40), (maxY/40) - (minY/40)))
        return minX, minY, maxX, maxY

class MotifEditor(QMainWindow):
    result_signal = Signal(bool)

    def __init__(self, config):
        super().__init__()
        self.setWindowTitle("Motif Editor")
        
        self.sm_config = config
        self.motif = Motif()
        
        # In MotifEditor.__init__, add to the menu bar setup:
        view_menu = self.menuBar().addMenu("View")
        view_tiles_action = QAction("View Tiles", self)
        view_tiles_action.triggered.connect(self.show_tile_viewer)
        view_menu.addAction(view_tiles_action)
        
        # Create InfoPane first
        self.info_pane = MotifInfoPane(self.motif)
        # Create StitchGrid with InfoPane reference
        self.stitch_grid = MotifGrid(self.info_pane, self.motif)
        


        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # Create container for grid and its controls
        grid_container = QWidget()
        grid_layout = QVBoxLayout(grid_container)
        grid_layout.addWidget(self.stitch_grid)
        grid_layout.addWidget(self.stitch_grid.controls)
        
        # Create and add Toggle Button
        #toggle_button = QPushButton("Toggle Line Weight")
        #toggle_button.clicked.connect(self.stitch_grid.toggle_weight)
        #grid_layout.addWidget(toggle_button)

        # Create and add Toggle Boundary Button
        toggle_boundary_button = QPushButton("Toggle Boundary")
        toggle_boundary_button.clicked.connect(self.toggle_boundary)
        grid_layout.addWidget(toggle_boundary_button)

        # Create and add Save Button
        save_button = QPushButton("Save Motif")
        save_button.clicked.connect(self.saveMotif)
        grid_layout.addWidget(save_button)

        # Create and add Load Button    
        load_button = QPushButton("Load Motif")
        load_button.clicked.connect(self.loadMotif)
        grid_layout.addWidget(load_button)
        
        # Create and add new motif button
        new_motif_button = QPushButton("New Motif")
        new_motif_button.clicked.connect(self.new_motif)
        grid_layout.addWidget(new_motif_button)

        # Create and add print motif button
        print_motif_button = QPushButton("Print Motif")
        print_motif_button.clicked.connect(self.motif.print_motif)
        grid_layout.addWidget(print_motif_button)

        # Add both to main layout
        main_layout.addWidget(grid_container)
        main_layout.addWidget(self.info_pane)
        
        self.resize(1000, 800)

    def show_tile_viewer(self):
        """Shows a dialog displaying all tiles from the current motif."""
        viewer = TileViewerDialog(self.motif, self)
        viewer.exec()

    def saveMotif(self):
        print("In MotifEditor.saveMotif()")
        self.motif.save_motif(self.sm_config.posMotifs_dir)

    def loadMotif(self):
        print("In MotifEditor.loadMotif()")
        print(f"Loading motif from {self.sm_config.posMotifs_dir}")
        if self.motif.choose_and_load_motif(self.sm_config.posMotifs_dir):
            # Update the info pane with the new motif name
            print(f"Loaded motif: {self.motif.name}")
            self.info_pane.name_edit.setText(self.motif.name)
            
            # Update motif grid
            self.stitch_grid.refresh_display()
            
            # Get extents for info pane update
            minX, minY, maxX, maxY = self.stitch_grid.findExtentsAcrossAllLayers()
            sizeX, sizeY = maxX - minX, maxY - minY
            
            # Update info pane with current state
            self.info_pane.update_info(
                len(self.motif.layers), 
                self.motif.current_layer.number,
                sizeX,
                sizeY
            )
            
            print(f"Successfully refreshed motif display")

    def new_motif(self):
        # Reset the motif
        self.motif.new()
        
        # Reset motif name
        self.motif.name = "motif"  # Reset to default name
        self.info_pane.name_edit.setText(self.motif.name)
        
        # Refresh the grid display
        self.stitch_grid.refresh_display()
        
        # Reset info pane with initial values
        self.info_pane.update_info(
            len(self.motif.layers),
            self.motif.current_layer.number,
            0,  # Initial size X
            0   # Initial size Y
        )

    def toggle_boundary(self):
        self.stitch_grid.toggle_boundary()
        self.stitch_grid.refresh_display()

class StitchWeightsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Stitch Weight Selection")
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # Create radio buttons
        self.single_radio = QRadioButton("Single Only")
        self.double_radio = QRadioButton("Double Only")
        self.both_radio = QRadioButton("Both Single and Double")
        
        # Add radio buttons to layout
        layout.addWidget(QLabel("Use Single or Double Stitches"))
        layout.addWidget(self.single_radio)
        layout.addWidget(self.double_radio)
        layout.addWidget(self.both_radio)
        
        # Add OK/Cancel buttons
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        # Set default selection
        self.single_radio.setChecked(True)

class GenerateOptionsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.project = parent.project
        self.config = parent.sm_config
        
        self.setWindowTitle("Generate Pattern Options")
        layout = QVBoxLayout()
        
        # Stitch type selection
        stitch_group = QGroupBox("Stitch Type")
        stitch_layout = QVBoxLayout()
        self.single_radio = QRadioButton("Single")
        self.double_radio = QRadioButton("Double")
        self.both_radio = QRadioButton("Single & Double")
        
        # Set default based on parent's stitch_type
        if self.parent.stitch_type == "Single":
            self.single_radio.setChecked(True)
        elif self.parent.stitch_type == "Double":
            self.double_radio.setChecked(True)
        else:
            self.both_radio.setChecked(True)
            
        stitch_layout.addWidget(self.single_radio)
        stitch_layout.addWidget(self.double_radio)
        stitch_layout.addWidget(self.both_radio)
        stitch_group.setLayout(stitch_layout)
        layout.addWidget(stitch_group)
    
        # Pattern Colours group
        colors_group = QGroupBox("Pattern Colours")
        colors_layout = QVBoxLayout()
        
        # Color options
        self.floss_only_radio = QRadioButton("Floss Colours Only")
        self.print1_radio = QRadioButton("Print 1 Default")
        self.print2_radio = QRadioButton("Print 2 Default")
        self.monochrome_radio = QRadioButton("Monochrome")
        
        # Set default to floss colors
        self.floss_only_radio.setChecked(True)
        
        colors_layout.addWidget(self.floss_only_radio)
        colors_layout.addWidget(self.print1_radio)
        colors_layout.addWidget(self.print2_radio)
        colors_layout.addWidget(self.monochrome_radio)
        
        colors_group.setLayout(colors_layout)
        layout.addWidget(colors_group)

        # File format selection
        format_group = QGroupBox("File Formats")
        format_layout = QGridLayout()
        
        # Pattern format selection
        format_layout.addWidget(QLabel("Pattern Format:"), 0, 0)
        self.pattern_format = QComboBox()
        self.pattern_format.addItems(["PDF", "SVG", "PNG"])
        # Set default from project or config
        default_pattern_format = self.project.pattern_file_format or self.config.pattern_file_format
        self.pattern_format.setCurrentText(default_pattern_format)
        format_layout.addWidget(self.pattern_format, 0, 1)
        
        # Picture format selection
        format_layout.addWidget(QLabel("Picture Format:"), 1, 0)
        self.picture_format = QComboBox()
        self.picture_format.addItems(["PDF", "SVG", "PNG"])
        # Set default from project or config
        default_picture_format = self.project.picture_file_format or self.config.picture_file_format
        self.picture_format.setCurrentText(default_picture_format)
        format_layout.addWidget(self.picture_format, 1, 1)
        
        format_group.setLayout(format_layout)
        layout.addWidget(format_group)

        # Page options
        page_group = QGroupBox("Page Options")
        page_layout = QVBoxLayout()
        
        # Single page checkbox
        self.single_page_check = QCheckBox("Generate Single Page Pattern")
        self.single_page_check.setChecked(True)
        page_layout.addWidget(self.single_page_check)
        
        # Split page options
        self.split_page_check = QCheckBox("Generate Split Page Pattern")
        page_layout.addWidget(self.split_page_check)
        
        split_options = QWidget()
        split_layout = QHBoxLayout()
        split_layout.addWidget(QLabel("Page Size (stitches):"))
        self.split_width = QSpinBox()
        self.split_width.setRange(5, 1000)
        self.split_width.setValue(50)
        self.split_height = QSpinBox()
        self.split_height.setRange(5, 1000)
        self.split_height.setValue(50)
        split_layout.addWidget(self.split_width)
        split_layout.addWidget(QLabel("x"))
        split_layout.addWidget(self.split_height)
        split_options.setLayout(split_layout)
        page_layout.addWidget(split_options)
        
        # Single color checkbox
        self.single_color_check = QCheckBox("Generate Single Color Patterns")
        page_layout.addWidget(self.single_color_check)
        
        page_group.setLayout(page_layout)
        layout.addWidget(page_group)
        
        # Dialog buttons
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)

    def get_options(self):
        # Get selected stitch type
        if self.single_radio.isChecked():
            stitch_type = "Single"
        elif self.double_radio.isChecked():
            stitch_type = "Double"
        else:
            stitch_type = "Single & Double"

        # Determine pattern color mode
        if self.floss_only_radio.isChecked():
            color_mode = 'floss'
        elif self.print1_radio.isChecked():
            color_mode = 'print1'
        elif self.print2_radio.isChecked():
            color_mode = 'print2'
        else:
            color_mode = 'monochrome'
    

        # Update project file formats if they've changed
        if self.project.pattern_file_format != self.pattern_format.currentText() or \
           self.project.picture_file_format != self.picture_format.currentText():
            self.project.pattern_file_format = self.pattern_format.currentText()
            self.project.picture_file_format = self.picture_format.currentText()
            self.project.save_project(self.parent.sm_config.posProjects_dir, self.parent.processor.amended_image)
            
        return {
            'stitch_type': stitch_type,
            'pattern_color_mode': color_mode,
            'single_page': self.single_page_check.isChecked(),
            'split_page': self.split_page_check.isChecked(),
            'split_width': self.split_width.value(),
            'split_height': self.split_height.value(),
            'single_color': self.single_color_check.isChecked(),
            'pattern_format': self.pattern_format.currentText(),
            'picture_format': self.picture_format.currentText()
        }


class ColorPaletteWindow(QMainWindow):
    update_image_labels_signal = Signal()
    result_signal = Signal(bool)

    def __init__(self, main_window=None, processor=None, brands=None, project=None):
        super().__init__()
        self.setWindowTitle("Set Palette and Reduce Colours")
        self.setGeometry(100, 100, 800, 900)
        self.main_window = main_window
        self.processor = processor
        self.all_brands = brands
        self.project = project
        self.locked_palette = []  # Store locked color-floss pairs

        # Initialize with previously assigned flosses if they exist
        if self.project and hasattr(self.project, 'flosses'):
            for floss in self.project.flosses:
                if floss not in self.locked_palette:  # Avoid duplicates
                    self.locked_palette.append(floss)

        # Create the source and reduced images
        if self.processor and hasattr(self.processor, 'amended_image'):
            self.source_image = self.processor.amended_image.copy()
            self.reduced_image = self.source_image.copy()
            self.amended_image = self.source_image.copy()

        # Create main widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # Create image section
        image_section = QWidget()
        image_layout = QHBoxLayout(image_section)

        # Source image
        amended_container = QWidget()
        amended_layout = QVBoxLayout(amended_container)
        amended_title = QLabel("Amended Image")
        amended_title.setAlignment(Qt.AlignCenter)
        self.amended_label = QLabel()
        self.amended_label.setFixedSize(350, 350)
        self.amended_label.setStyleSheet("border: 1px solid black")
        amended_layout.addWidget(amended_title)
        amended_layout.addWidget(self.amended_label)
        image_layout.addWidget(amended_container)
        self.amended_label.setMouseTracking(True)  # Enable mouse tracking
        self.amended_label.installEventFilter(self)  # Install event filter

        # Reduced image
        reduced_container = QWidget()
        reduced_layout = QVBoxLayout(reduced_container)
        reduced_title = QLabel("Reduced Image")
        reduced_title.setAlignment(Qt.AlignCenter)
        self.reduced_label = QLabel()
        self.reduced_label.setFixedSize(350, 350)
        self.reduced_label.setStyleSheet("border: 1px solid black")
        reduced_layout.addWidget(reduced_title)
        reduced_layout.addWidget(self.reduced_label)
        image_layout.addWidget(reduced_container)

        main_layout.addWidget(image_section)

        # Add horizontal line
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line1)

        # Locked Colors section
        locked_colors_title = QLabel("Locked Colours")
        locked_colors_title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(locked_colors_title)

        # Scrollable grid for locked colors
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        self.locked_colors_layout = QGridLayout(scroll_content)
        scroll_area.setWidget(scroll_content)
        scroll_area.setMaximumHeight(200)
        main_layout.addWidget(scroll_area)

        # Add second horizontal line
        line2 = QFrame()
        line2.setFrameShape(QFrame.HLine)
        line2.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line2)

        # Add color preview section above the buttons
        preview_container = QWidget()
        preview_layout = QHBoxLayout(preview_container)
        
        # Current color preview
        self.current_color_swatch = QLabel()
        self.current_color_swatch.setFixedSize(60, 60)
        self.current_color_swatch.setStyleSheet("background-color: white; border: 2px solid black;")
        
        # RGB value label
        self.current_color_rgb = QLabel("RGB: ---")
        self.current_color_rgb.setStyleSheet("background-color: white; padding: 4px;")
        
        preview_layout.addWidget(self.current_color_swatch)
        preview_layout.addWidget(self.current_color_rgb)
        preview_layout.addStretch()  # Push widgets to the left
        
        main_layout.addWidget(preview_container)

        # Add floss input section above the buttons
        floss_input_container = QWidget()
        floss_input_layout = QHBoxLayout(floss_input_container)
        
        self.floss_input = QLineEdit()
        self.floss_input.setPlaceholderText("Enter floss name (e.g. DMC 310)")
        self.floss_input.setFixedWidth(200)
        
        self.add_floss_btn = QPushButton("Add Floss")
        self.add_floss_btn.clicked.connect(self.add_floss_by_name)
        
        floss_input_layout.addWidget(self.floss_input)
        floss_input_layout.addWidget(self.add_floss_btn)
        floss_input_layout.addStretch()  # Push widgets to the left
        
        main_layout.addWidget(floss_input_container)


        # Control buttons
        button_layout = QHBoxLayout()
        
        self.auto_assign_btn = QPushButton("Auto Assign")
        #self.manual_select_btn = QPushButton("Manual Select")
        self.clear_palette_btn = QPushButton("Clear Palette")
        #self.reduce_colors_btn = QPushButton("Reduce Colours")
        #self.undo_reduction_btn = QPushButton("Undo Reduction")
        self.save_btn = QPushButton("Save")
        self.cancel_btn = QPushButton("Cancel")

        # Connect button signals
        self.auto_assign_btn.clicked.connect(self.auto_assign)
        #self.manual_select_btn.clicked.connect(self.manual_select)
        self.clear_palette_btn.clicked.connect(self.clear_palette)
        #self.reduce_colors_btn.clicked.connect(self.reduce_colors)
        #self.undo_reduction_btn.clicked.connect(self.undo_reduction)
        self.save_btn.clicked.connect(self.save_changes)
        self.cancel_btn.clicked.connect(self.cancel)

        # Add buttons to layout
        button_layout.addWidget(self.auto_assign_btn)
        #button_layout.addWidget(self.manual_select_btn)
        button_layout.addWidget(self.clear_palette_btn)
        #button_layout.addWidget(self.reduce_colors_btn)
        #button_layout.addWidget(self.undo_reduction_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)

        main_layout.addLayout(button_layout)

        # Initialize images
        self.amended_image = self.processor.amended_image.copy()
        self.reduced_image = self.amended_image.copy()
        self.update_image_labels()

        self.setMouseTracking(True)

    def add_floss_by_name(self):
        """Add a floss to the locked palette by name"""
        floss_name = self.floss_input.text().strip()
        if not floss_name:
            QMessageBox.warning(self, "Warning", "Please enter a floss name")
            return

        # Search for floss in all brands
        found_floss = None
        for brand in self.all_brands.brands:  # Access the brands list from the Brands instance
            for floss in brand.flosses:
                if floss.name.lower() == floss_name.lower():
                    found_floss = floss
                    break
            if found_floss:
                break

        if not found_floss:
            QMessageBox.warning(self, "Warning", f"Floss '{floss_name}' not found in any brand")
            return

        # Check if floss is already in palette
        if found_floss in self.locked_palette:
            QMessageBox.information(self, "Info", f"Floss '{floss_name}' is already in the palette")
            return

        # Add floss to palette
        success = self.add_locked_color(found_floss)
        if success:
            self.floss_input.clear()  # Clear the input field
            self.update_reduced_image()  # Update the reduced image
        else:
            QMessageBox.warning(self, "Warning", "Failed to add floss (palette might be full)")

    def update_image_labels(self):
        """Update both image labels with current images"""
        def set_image(image, label):
            height, width, channel = image.shape
            bytes_per_line = 3 * width
            q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_BGR888)
            pixmap = QPixmap.fromImage(q_image)
            label.setPixmap(pixmap.scaled(label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))

        set_image(self.amended_image, self.amended_label)
        set_image(self.reduced_image, self.reduced_label)
        self.amended_label.setMouseTracking(True)  # Ensure mouse tracking is enabled
            
        # Make sure event filter is installed
        self.amended_label.installEventFilter(self)

    def add_locked_color(self, floss):
        """Add a floss to the locked palette grid with right-click to clear"""
        if len(self.locked_palette) >= 256:
            QMessageBox.warning(self, "Warning", "Maximum number of colors (256) reached")
            return False
            
        row = len(self.locked_palette) // 4
        col = len(self.locked_palette) % 4

        # Create container for floss display
        container = QWidget()
        layout = QHBoxLayout(container)
        
        # Color display
        color_box = QLabel()
        color_box.setFixedSize(30, 30)
        color_box.setStyleSheet(f"background-color: rgb({floss.rgb[0]}, {floss.rgb[1]}, {floss.rgb[2]}); border: 1px solid black")
        
        # Floss name
        name_label = QLabel(f"{floss.name}")
        
        layout.addWidget(color_box)
        layout.addWidget(name_label)
        
        # Install event filter on container to handle right-clicks
        container.installEventFilter(self)
        # Store floss reference in container
        container.floss = floss
        
        self.locked_colors_layout.addWidget(container, row, col)
        self.locked_palette.append(floss)
        
        return True

    def clear_locked_colors(self):
        """Remove all locked colors from the grid"""
        while self.locked_colors_layout.count():
            item = self.locked_colors_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        self.locked_palette.clear()

    def auto_assign(self):
        """Automatically assign floss matches to the most prominent colors"""
        # Get desired number of colors from user
        num_colors, ok = QInputDialog.getInt(
            self,  # parent
            "Auto Assign Colors",  # title
            "Enter total number of colors desired:",  # label
            len(self.locked_palette) + 1,  # default value 
            len(self.locked_palette),  # minimum value
            256  # maximum value
        )
        
        if not ok:
            return

        # Calculate how many new colors we need
        colors_to_add = num_colors - len(self.locked_palette)
        if colors_to_add <= 0:
            QMessageBox.information(self, "Info", "No additional colors needed")
            return

        # Get existing floss RGB values to avoid duplicates
        existing_rgb = {tuple(floss.rgb) for floss in self.locked_palette}

        # Get color frequencies from source image
        pixels = self.amended_image.reshape(-1, 3)
        unique_colors, counts = np.unique(pixels, axis=0, return_counts=True)
        
        # Sort colors by frequency (most common first)
        color_freq = list(zip(unique_colors, counts))
        color_freq.sort(key=lambda x: x[1], reverse=True)
        
        # Find most prominent colors not already represented
        new_colors = []
        for color, _ in color_freq:
            # Convert BGR to RGB for comparison
            rgb = (int(color[2]), int(color[1]), int(color[0]))
            
            # Skip if this color is already represented
            if self._is_color_represented(rgb, existing_rgb):
                continue
                
            # Find closest floss match
            closest_floss = self._find_closest_floss(rgb)
            
            # Skip if this floss is already in palette
            if closest_floss in self.locked_palette:
                continue
                
            # Add floss and update existing colors
            new_colors.append(closest_floss)
            existing_rgb.add(tuple(closest_floss.rgb))
            
            # Break if we have enough colors
            if len(new_colors) >= colors_to_add:
                break
        
        # Add new colors to locked palette
        for floss in new_colors:
            self.add_locked_color(floss)
        self.update_reduced_image()

    def rebuild_floss_grid(self):
        """Rebuild the grid layout with current locked flosses and update reduced image"""
        # Remove all widgets from layout
        while self.locked_colors_layout.count():
            item = self.locked_colors_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
                
        # Rebuild grid with remaining flosses
        for i, floss in enumerate(self.locked_palette):
            row = i // 4
            col = i % 4
            
            # Create container for floss display
            container = QWidget()
            layout = QHBoxLayout(container)
            
            # Color display
            color_box = QLabel()
            color_box.setFixedSize(30, 30)
            color_box.setStyleSheet(f"background-color: rgb({floss.rgb[0]}, {floss.rgb[1]}, {floss.rgb[2]}); border: 1px solid black")
            
            # Floss name
            name_label = QLabel(f"{floss.name}")
            
            layout.addWidget(color_box)
            layout.addWidget(name_label)
            
            # Install event filter and store floss reference
            container.installEventFilter(self)
            container.floss = floss
            
            self.locked_colors_layout.addWidget(container, row, col)
        
        # Update the reduced image
        self.update_reduced_image()

    def _is_color_represented(self, rgb, existing_rgb, threshold=30):
        """Check if a color is already represented in existing colors"""
        for existing in existing_rgb:
            distance = sum((a - b) ** 2 for a, b in zip(rgb, existing)) ** 0.5
            if distance < threshold:
                return True
        return False

    def _find_closest_floss(self, rgb):
        """Find the closest floss match for an RGB color"""
        closest_floss = None
        min_distance = float('inf')
        
        for brand in self.all_brands.brands:
            for floss in brand.flosses:
                distance = sum((a - b) ** 2 for a, b in zip(rgb, floss.rgb)) ** 0.5
                if distance < min_distance:
                    min_distance = distance
                    closest_floss = floss
                    
        return closest_floss

    def manual_select(self):
        """Enable manual color selection mode"""
        # Store reference to ColorPaletteWindow for access in event filter
        self.active_window = self
        
        # Install event filter on application
        QApplication.instance().installEventFilter(self)
        
        # Change cursor
        QApplication.setOverrideCursor(Qt.CrossCursor)
        
        # Update status
        self.statusBar().showMessage("Click to select a color. Press ESC to cancel.")


    def eventFilter(self, obj, event):
        """Handle mouse events for color selection and floss management"""
        # Add debug print to verify event handling
        if isinstance(obj, QLabel) and obj == self.amended_label:
            if event.type() == QEvent.MouseButtonPress:
                #print("Mouse press detected on amended image")
                if event.button() == Qt.LeftButton:
                    # Get color under cursor
                    color = self.get_screen_color(event.globalPos())
                    # Find closest floss and add to palette if not already present
                    self.handle_color_picked(color)
                    return True
            
            elif event.type() == QEvent.MouseMove:
                #print("Mouse move detected on amended image")
                color = self.get_screen_color(event.globalPos())
                self.update_color_preview(color)
                return True

        # Handle locked floss container events
        if hasattr(obj, 'floss'):
            if event.type() == QEvent.MouseButtonPress and event.button() == Qt.RightButton:
                if obj.floss in self.locked_palette:
                    self.locked_palette.remove(obj.floss)
                obj.setParent(None)
                obj.deleteLater()
                self.rebuild_floss_grid()
                return True

        return super().eventFilter(obj, event)

    def update_reduced_image(self):
        """Update the reduced image using optimized CIEDE2000 color matching"""
        if not self.locked_palette:
            self.reduced_image = self.amended_image.copy()
            return

        height, width = self.amended_image.shape[:2]
        
        # Convert target colors to BGR format
        target_colors_bgr = np.array([floss.rgb[::-1] for floss in self.locked_palette], dtype=np.uint8)

        # Convert to LAB colorspace
        lab_image = cv2.cvtColor(self.amended_image, cv2.COLOR_BGR2LAB)
        pixels_lab = lab_image.reshape(-1, 3)
        target_colors_lab = cv2.cvtColor(target_colors_bgr.reshape(1, -1, 3), cv2.COLOR_BGR2LAB).reshape(-1, 3)

        # Process in batches to avoid memory issues
        batch_size = 10000
        num_pixels = pixels_lab.shape[0]
        closest_indices = np.zeros(num_pixels, dtype=np.int32)
        
        for i in range(0, num_pixels, batch_size):
            batch_end = min(i + batch_size, num_pixels)
            batch_pixels = pixels_lab[i:batch_end]
            distances = self.delta_e_2000_optimized(batch_pixels, target_colors_lab)
            closest_indices[i:batch_end] = np.argmin(distances, axis=1)

        # Map back to target colors
        reduced_pixels = target_colors_bgr[closest_indices]
        self.reduced_image = reduced_pixels.reshape(height, width, 3)
        
        # Update display
        self.update_image_labels()

    @staticmethod
    def delta_e_2000_optimized(lab1, lab2):
        """
        Highly optimized CIEDE2000 implementation using NumPy with overflow protection
        lab1: Nx3 array of LAB colors
        lab2: Mx3 array of target LAB colors
        Returns: NxM array of color differences
        """
        def safe_power(x, power):
            """Safely compute power with overflow protection"""
            if isinstance(x, (int, float)):
                # Handle scalar values
                return min(pow(float(x), power), 1e300) if x != 0 else 0
            else:
                # Handle numpy arrays
                x = x.astype(np.float64)
                x = np.clip(x, -1e300, 1e300)
                return np.power(x, power, where=(x != 0))

        def safe_divide(a, b):
            """Safely divide with handling for zero division"""
            return np.divide(a, b, out=np.zeros_like(a, dtype=np.float64), where=b != 0)

        def safe_sqrt(x):
            """Safely compute square root with handling for negative values"""
            return np.sqrt(np.maximum(x, 0))

        # Reshape for broadcasting
        lab1 = np.asarray(lab1, dtype=np.float64).reshape(-1, 1, 3)  # N x 1 x 3
        lab2 = np.asarray(lab2, dtype=np.float64).reshape(1, -1, 3)  # 1 x M x 3

        # Constants
        k_L = 1.0
        k_C = 1.0
        k_H = 1.0

        # Convert to L*a*b* ranges
        L1, a1, b1 = lab1[..., 0] / 255.0 * 100, lab1[..., 1] - 128, lab1[..., 2] - 128
        L2, a2, b2 = lab2[..., 0] / 255.0 * 100, lab2[..., 1] - 128, lab2[..., 2] - 128

        # Calculate C1, C2, C_ave
        C1 = safe_sqrt(a1 * a1 + b1 * b1)
        C2 = safe_sqrt(a2 * a2 + b2 * b2)
        C_ave = (C1 + C2) / 2.0

        # Calculate G
        C_ave_7 = safe_power(C_ave, 7)
        G = 0.5 * (1.0 - safe_sqrt(safe_divide(C_ave_7, (C_ave_7 + safe_power(25.0, 7)))))

        # Calculate a'1, a'2
        a1_p = (1.0 + G) * a1
        a2_p = (1.0 + G) * a2

        # Calculate C'1, C'2
        C1_p = safe_sqrt(a1_p * a1_p + b1 * b1)
        C2_p = safe_sqrt(a2_p * a2_p + b2 * b2)

        # Calculate h'1, h'2
        h1_p = np.arctan2(b1, a1_p) % (2 * np.pi)
        h2_p = np.arctan2(b2, a2_p) % (2 * np.pi)

        # Calculate ΔL', ΔC', ΔH'
        delta_L_p = L2 - L1
        delta_C_p = C2_p - C1_p

        # Calculate ΔH'
        delta_h_p = h2_p - h1_p
        delta_h_p = np.where(delta_h_p > np.pi, delta_h_p - 2 * np.pi, delta_h_p)
        delta_h_p = np.where(delta_h_p < -np.pi, delta_h_p + 2 * np.pi, delta_h_p)
        
        delta_H_p = 2.0 * safe_sqrt(C1_p * C2_p) * np.sin(delta_h_p / 2.0)

        # Calculate L', C', H' averages
        L_p = (L1 + L2) / 2.0
        C_p = (C1_p + C2_p) / 2.0
        
        # Calculate h'_ave
        h_p_ave = (h1_p + h2_p) / 2.0
        mask = np.abs(h1_p - h2_p) > np.pi
        h_p_ave[mask] = (h_p_ave[mask] + np.pi) % (2 * np.pi)

        # Calculate T
        T = (1.0 - 
            0.17 * np.cos(h_p_ave - np.pi/6.0) +
            0.24 * np.cos(2.0 * h_p_ave) +
            0.32 * np.cos(3.0 * h_p_ave + np.pi/30.0) -
            0.20 * np.cos(4.0 * h_p_ave - 63.0 * np.pi/180.0))

        # Calculate R_C
        C_p_7 = safe_power(C_p, 7)
        R_C = 2.0 * safe_sqrt(safe_divide(C_p_7, (C_p_7 + safe_power(25.0, 7))))
        
        # Calculate S_L, S_C, S_H
        L_p_50_squared = safe_power(L_p - 50.0, 2)
        S_L = 1.0 + (0.015 * L_p_50_squared) / safe_sqrt(20.0 + L_p_50_squared)
        S_C = 1.0 + 0.045 * C_p
        S_H = 1.0 + 0.015 * C_p * T

        # Calculate R_T
        theta = 30.0 * np.pi / 180.0
        R_T = -np.sin(2.0 * theta) * R_C

        # Final calculation
        delta_L_term = safe_divide(delta_L_p, k_L * S_L)
        delta_C_term = safe_divide(delta_C_p, k_C * S_C)
        delta_H_term = safe_divide(delta_H_p, k_H * S_H)

        delta_E = safe_sqrt(
            delta_L_term ** 2 +
            delta_C_term ** 2 +
            delta_H_term ** 2 +
            R_T * delta_C_term * delta_H_term
        )

        # Handle any remaining invalid values
        return np.nan_to_num(delta_E, nan=float('inf'), posinf=float('inf'))

    def get_screen_color(self, pos):
        """Get color at screen position"""
        screen = QApplication.primaryScreen()
        pixmap = screen.grabWindow(0, pos.x(), pos.y(), 1, 1)
        image = pixmap.toImage()
        return QColor(image.pixel(0, 0))

    def update_color_preview(self, color):
        """Update the color preview widgets"""
        self.current_color_swatch.setStyleSheet(
            f"background-color: rgb({color.red()},{color.green()},{color.blue()}); "
            "border: 2px solid black;"
        )
        self.current_color_rgb.setText(f"RGB: {color.red()}, {color.green()}, {color.blue()}")

    def end_color_selection(self):
        """Clean up after color selection"""
        QApplication.instance().removeEventFilter(self)
        QApplication.restoreOverrideCursor()
        self.statusBar().clearMessage()

    def handle_color_picked(self, color):
        """Handle picked color and find closest floss match"""
        rgb = (color.red(), color.green(), color.blue())
        
        # Find closest floss match
        closest_floss = None
        min_distance = float('inf')
        
        for brand in self.all_brands.brands:
            for floss in brand.flosses:
                # Calculate color distance using RGB values
                distance = sum((a - b) ** 2 for a, b in zip(rgb, floss.rgb))
                if distance < min_distance:
                    min_distance = distance
                    closest_floss = floss
        
        if closest_floss:
            # Add to locked colors if not already present
            if closest_floss not in self.locked_palette:
                success = self.add_locked_color(closest_floss)
                if not success:
                    QMessageBox.warning(self, "Warning", 
                        "Maximum number of colors (256) reached")
                else:
                    # Rebuild the grid and update the reduced image
                    self.rebuild_floss_grid()
                
    def clear_palette(self):
        """Clear the current color palette"""
        self.clear_locked_colors()


    def save_changes(self):
        """Save changes and update project"""
        self.processor.amended_image = self.reduced_image.copy()
        self.processor.history.append(self.processor.amended_image.copy())
        # Update project's assigned flosses with locked colors
        self.project.assigned_flosses = {}  # Clear existing assignments
        for floss in self.locked_palette:
            # Use RGB tuple as key for the floss assignment
            rgb = tuple(floss.rgb)  # Convert RGB list to tuple for dict key
            self.project.assigned_flosses[rgb] = floss
        
        if hasattr(self, 'main_window') and self.main_window:
            # Update project's assigned flosses with locked colors
            self.main_window.project.assigned_flosses = {}  # Clear existing assignments
            for floss in self.locked_palette:
                # Use RGB tuple as key for the floss assignment
                rgb = tuple(floss.rgb)  # Convert RGB list to tuple for dict key
                self.main_window.project.assigned_flosses[rgb] = floss
            
            # Update project's flosses list
            self.main_window.project.flosses = list(self.locked_palette)
            
            # Update the info pane in the main window
            self.main_window.update_info_pane()

        # Signal updates and close
        self.update_image_labels_signal.emit()
        self.result_signal.emit(True)
        self.close()

    def cancel(self):
        """Cancel all changes and close window"""
        self.result_signal.emit(False)
        self.close()


class MainWindow(QMainWindow):
    def __init__(self, brands, config):
        super().__init__()

        global VERSION
        global BUTTON_WIDTH
        self.tileSize=10
        
        # Set up the main window
        sTitle="Stitch Master "+VERSION
        self.setWindowTitle(sTitle)
        self.setGeometry(100, 100, 800, 600)
        self.project = Project()
        self.processor = ImageProcessor()
        self.processor.set_config(config)
        self.sm_config = config
        self.config=config
        self.all_brands = brands
        self.bUpdated=False
        #self.edit_window = None
        #self.assign_flosses_window = None
        #self.motif_window = None
        self.selected_motifs = []  # Add this to __init__
        #self._view_flosses_window = None

        # Initialize window references
        self._view_flosses_window = None
        self.edit_window = None
        self.assign_flosses_window = None
        self.motif_window = None
        self.floss_image_dialog = None

        # Primary images
        self.patImage = None

        # Create the menu bar
        menu_bar = self.menuBar()

        # Create the toolbar
        toolbar = QToolBar("Main Toolbar")
        self.addToolBar(toolbar)


        # Add toolbar items
        open_action = QAction(QIcon("open_icon.png"), "Open", self)
        open_action.triggered.connect(self.open_image)
        toolbar.addAction(open_action)

        save_action = QAction(QIcon("save_icon.png"), "Save", self)
        save_action.triggered.connect(self.saveProject)
        toolbar.addAction(save_action)

        # Add separator
        toolbar.addSeparator()

        # Add Manual Color Reduction button
        #self.manual_color_reduction_btn = QPushButton("Manual Colour Reduction")
        #self.manual_color_reduction_btn.clicked.connect(self.open_color_palette_window)
        #toolbar.addWidget(self.manual_color_reduction_btn)

        # Add separator
        toolbar.addSeparator()

        # Project menu
        project_menu = menu_bar.addMenu("Project")

        new_project_action = QAction("New Project", self)
        new_project_action.triggered.connect(self.newProject)
        project_menu.addAction(new_project_action)

        open_project_action = QAction("Open Project", self)
        open_project_action.triggered.connect(self.openProject)
        project_menu.addAction(open_project_action)

        save_project_action = QAction("Save Project", self)
        save_project_action.triggered.connect(self.saveProject)
        project_menu.addAction(save_project_action)

        project_info_action = QAction("Project Information", self)
        project_info_action.triggered.connect(self.displayProject)
        project_menu.addAction(project_info_action)

        next_info_action = QAction("To be worked on Next", self)
        next_info_action.triggered.connect(lambda: self.displayInfo("Next"))
        project_menu.addAction(next_info_action)

        bugs_info_action = QAction("Known Bugs", self)
        bugs_info_action.triggered.connect(lambda: self.displayInfo("Bugs"))
        project_menu.addAction(bugs_info_action)

        ui_info_action = QAction("Known User Interface Improvements", self)
        ui_info_action.triggered.connect(lambda: self.displayInfo("UI"))
        project_menu.addAction(ui_info_action)

        enh_info_action = QAction("Planned Enhancements", self)
        enh_info_action.triggered.connect(lambda: self.displayInfo("Enh"))
        project_menu.addAction(enh_info_action)

        export_pal_action = QAction("Export Legacy Pal File", self)
        export_pal_action.triggered.connect(self.export_pal_file)
        project_menu.addAction(export_pal_action)

        print_flosses_action = QAction("Export Legacy Pal File", self)
        print_flosses_action.triggered.connect(self.project.printFlosses)
        project_menu.addAction(print_flosses_action)

        """Add View Project Data action to the Project menu"""
        view_project_action = QAction("View project data...", self)
        view_project_action.setStatusTip("View all data in a project file")
        view_project_action.triggered.connect(self.show_project_data)
        project_menu.addAction(view_project_action)
     
        # Motif menu
        motif_menu = menu_bar.addMenu("Motif")
        new_motif_action = QAction("New Motif", self)
        new_motif_action.triggered.connect(self.showMotifWindow)
        motif_menu.addAction(new_motif_action)

        motif_library_action = QAction("Motif Library", self)
        #motif_library_action.triggered.connect(self.motif_library)
        motif_menu.addAction(motif_library_action)

        # View menu    
        view_menu = menu_bar.addMenu("View")
        """view_src_image_action = QAction("Source Image", self, checkable=True)
        view_src_image_action.setChecked(True)
        view_src_image_action.triggered.connect(self.toggle_src_image)
        view_menu.addAction(view_src_image_action)

        view_amended_image_action = QAction("Amended Image", self, checkable=True)
        view_amended_image_action.setChecked(True)
        view_amended_image_action.triggered.connect(self.toggle_amended_image)
        view_menu.addAction(view_amended_image_action)"""

        self.view_flosses_action = QAction("Available Flosses", self)  # Remove checkable=True
        self.view_flosses_action.triggered.connect(self.show_view_flosses)  # Connect directly to show
        view_menu.addAction(self.view_flosses_action)
        # Initialize view flosses window reference
        self._view_flosses_window = None

        edit_image_action = QAction("Edit Image", self)
        edit_image_action.triggered.connect(lambda: self.show_edit_window())
        view_menu.addAction(edit_image_action)

        assign_flosses_window_action = QAction("Assign Flosses", self)
        assign_flosses_window_action.triggered.connect(self.show_assign_flosses_window)
        view_menu.addAction(assign_flosses_window_action)

        # Configuration menu
        configuration_menu = menu_bar.addMenu("Configuration")
        floss_menu = configuration_menu.addMenu("Floss")
        update_brands_action = QAction("Update Brands", self)
        update_flosses_action = QAction("Update Flosses", self)
        update_brands_action.triggered.connect(self.update_brands)
        configuration_menu.addAction(update_brands_action)
        update_flosses_action.triggered.connect(self.update_flosses)
        configuration_menu.addAction(update_flosses_action)

        # Settings menu - move this here with other menus
        settings_menu = menu_bar.addMenu("Settings")
        configure_action = QAction("Configure...", self)
        configure_action.triggered.connect(self.show_settings)
        settings_menu.addAction(configure_action)      

        # Ensure menu bar shows up properly on macOS
        self.setMenuBar(menu_bar)


        ### Create the Widgets 
        # Image boxes
        self.src_image_label = QLabel("No Image Opened")
        self.src_image_label.setAlignment(Qt.AlignCenter)
        self.src_image_label.setStyleSheet("background-color: lightgray; border: 1px solid black;")
        self.src_image_label.setFixedSize(300,300)

        self.amended_image_label = QLabel("No Image Opened")
        self.amended_image_label.setAlignment(Qt.AlignCenter)
        self.amended_image_label.setStyleSheet("background-color: lightgray; border: 1px solid black;")
        self.amended_image_label.setFixedSize(300,300)

        self.picture_image_label = QLabel("No Pattern Generated")
        self.picture_image_label.setAlignment(Qt.AlignCenter)
        self.picture_image_label.setStyleSheet("background-color: lightgray; border: 1px solid black;")
        self.picture_image_label.setFixedSize(300,300)

        # Src Info
        self.labelSrcTitle=QLabel("Source Image")
        self.labelSrcColours=QLabel("# Colours: %d" % (self.processor.original_image_colours))
        self.labelSrcDimensions=QLabel("# Stitches Wide: %d    Stitches High %d" % (self.processor.iOriginalWidth, self.processor.iOriginalHeight))
        
        # Amended Info
        self.labelAmendedTitle=QLabel("Amended Image")
        self.labelAmendedColours=QLabel("# Colours: %d" % (self.processor.amended_image_colours))
        self.labelAmendedDimensions=QLabel("# Stitches Wide: %d    Stitches High %d" % (self.processor.iAmendedWidth, self.processor.iAmendedHeight))

        # Layout time
        layout = QVBoxLayout()
        
        # Create title labels with center alignment
        source_title = QLabel("Original Image")
        source_title.setAlignment(Qt.AlignCenter)
        current_title = QLabel("Current Image")
        current_title.setAlignment(Qt.AlignCenter)
        picture_title = QLabel("Generated Picture")
        picture_title.setAlignment(Qt.AlignCenter)
        
        # Create containers for each image and its title
        src_container = QVBoxLayout()
        src_container.addWidget(source_title)
        src_container.addWidget(self.src_image_label)
        
        current_container = QVBoxLayout()
        current_container.addWidget(current_title)
        current_container.addWidget(self.amended_image_label)
        
        pattern_container = QVBoxLayout()
        pattern_container.addWidget(picture_title)
        pattern_container.addWidget(self.picture_image_label)
        
        # Image layout to put labels side by side 
        image_layout = QHBoxLayout()
        
        # Add containers instead of just the labels
        image_layout.addLayout(src_container)
        image_layout.addLayout(current_container)
        image_layout.addLayout(pattern_container)
        
        layout.addLayout(image_layout)

        #self.color_input = QLineEdit(self)
        #self.color_input.setFixedWidth(BUTTON_WIDTH)
        #self.amendedImageColours=self.processor.get_number_of_colors(self.processor.amended_image)
        #self.color_input.setValidator(QIntValidator(1, self.processor.amended_image_colours))
        #self.color_input.setPlaceholderText(f"(1-{self.processor.amended_image_colours})")

        #self.reduce_button = QPushButton("Reduce colours", self)
        #self.reduce_button.setFixedWidth(BUTTON_WIDTH)
        #self.reduce_button.clicked.connect(self.reduce_colors)

        self.size_input = QLineEdit(self)
        self.size_input.setFixedWidth(BUTTON_WIDTH)
        self.size_input.setValidator(QIntValidator(1, self.processor.iAmendedMaxStitches))
        self.size_input.setPlaceholderText(f"(1-{self.processor.iAmendedMaxStitches})")

        self.reduce_size_button = QPushButton("Reduce size", self)
        self.reduce_size_button.setFixedWidth(BUTTON_WIDTH)
        self.reduce_size_button.clicked.connect(self.reduce_size)

        self.flosses_button = QPushButton("Assign Flosses", self)
        self.flosses_button.setFixedWidth(BUTTON_WIDTH)
        self.flosses_button.clicked.connect(self.open_color_palette_window)

        # Add to MainWindow.__init__ after other buttons:
        self.crop_button = QPushButton("Crop to Image", self)
        self.crop_button.setFixedWidth(BUTTON_WIDTH)
        self.crop_button.clicked.connect(self.crop_to_image)

        # Add after crop button in toolbar
        self.select_background_button = QPushButton("Select Background")
        self.select_background_button.setFixedWidth(BUTTON_WIDTH)
        self.select_background_button.clicked.connect(self.start_background_selection)
        self.select_background_button.setStyleSheet("background-color: white;")



        self.undo_button = QPushButton("Undo", self)
        self.undo_button.setFixedWidth(BUTTON_WIDTH)
        self.undo_button.clicked.connect(self.undo)

        self.redo_button = QPushButton("Redo", self)
        self.redo_button.setFixedWidth(BUTTON_WIDTH)
        self.redo_button.clicked.connect(self.redo)

        self.generate_pattern_button = QPushButton("Generate Pattern", self)
        self.generate_pattern_button.setFixedWidth(BUTTON_WIDTH)
        self.generate_pattern_button.clicked.connect(self.generate_pattern)
        self.generate_pattern_button.setEnabled(False)  # Disabled by default
    
        self.choose_motifs_button = QPushButton("Choose Motifs", self)
        self.choose_motifs_button.setFixedWidth(BUTTON_WIDTH)
        self.choose_motifs_button.clicked.connect(self.choose_motifs)

        # Add new stitch weights button
        self.stitch_weights_button = QPushButton("Stitch Weights", self)
        self.stitch_weights_button.setFixedWidth(BUTTON_WIDTH)
        self.stitch_weights_button.clicked.connect(self.show_stitch_weights_dialog)

        # Add stitch_type to track the selected option
        self.stitch_type = "Single"  # Default value

        # Add color toggle button
        #self.colour_output_button = QPushButton("Color Output: Yes", self)
        #self.colour_output_button.setFixedWidth(BUTTON_WIDTH)
        #self.colour_output_button.setCheckable(True)  # Make it toggleable
        #self.colour_output_button.clicked.connect(self.toggle_color_output)
        #self.use_colour = True  # Default to colour

        # Layout for Controls
        control_layout = QGridLayout()
        #control_layout.addWidget((self.color_input),0,0)
        #control_layout.addWidget((self.reduce_button),0,1)
        control_layout.addWidget((self.size_input),0,0)
        control_layout.addWidget((self.reduce_size_button),0,1)
        control_layout.addWidget((self.flosses_button),0,2)
        control_layout.addWidget((self.crop_button),0,3)
        #control_layout.addWidget((self.mono_button),0,5)


        control_layout.addWidget((self.select_background_button),0,5)
        control_layout.addWidget((self.choose_motifs_button),1,0)
        control_layout.addWidget((self.stitch_weights_button),1,1)
        #control_layout.addWidget((self.colour_output_button),1,2) 
        control_layout.addWidget((self.generate_pattern_button),1,3)
        control_layout.addWidget((self.undo_button),1,4)
        control_layout.addWidget((self.redo_button),1,5)
        layout.addLayout(control_layout)

        # Create info pane
        info_pane = QWidget()
        info_layout = QVBoxLayout()
        
        # Project Section
        project_group = QGroupBox("Project")
        project_layout = QFormLayout()
        self.project_name_label = QLabel("No project loaded")
        self.project_saved_label = QLabel("Never saved")
        project_layout.addRow("Name:", self.project_name_label)
        project_layout.addRow("Last Saved:", self.project_saved_label)
        project_group.setLayout(project_layout)
        
        # Pattern Section
        pattern_group = QGroupBox("Pattern")
        pattern_layout = QFormLayout()
        self.pattern_generated_label = QLabel("Not generated")
        self.pattern_size_label = QLabel("0 x 0")
        self.pattern_flosses_label = QLabel("0")
        self.pattern_motifs_label = QLabel("None")
        self.pattern_stitch_types_label = QLabel(self.stitch_type)
        pattern_layout.addRow("Generated:", self.pattern_generated_label)
        pattern_layout.addRow("Size:", self.pattern_size_label)
        pattern_layout.addRow("Flosses:", self.pattern_flosses_label)
        pattern_layout.addRow("Motifs:", self.pattern_motifs_label)
        pattern_layout.addRow("Stitch Types:", self.pattern_stitch_types_label)
        pattern_group.setLayout(pattern_layout)

        # Add Stitches section to info pane
        stitches_group = QGroupBox("Stitches")
        stitches_layout = QFormLayout()
        self.stitches_total_label = QLabel("0")
        self.stitches_single_label = QLabel("0")
        self.stitches_double_label = QLabel("0")
        self.stitches_by_floss_label = QLabel("None")
        stitches_layout.addRow("Total:", self.stitches_total_label)
        stitches_layout.addRow("Single:", self.stitches_single_label)
        stitches_layout.addRow("Double:", self.stitches_double_label)
        stitches_layout.addRow("By Floss:", self.stitches_by_floss_label)
        stitches_group.setLayout(stitches_layout)
                
        # Source Section
        source_group = QGroupBox("Source")
        source_layout = QFormLayout()
        self.source_filename_label = QLabel("No file loaded")
        self.source_dimensions_label = QLabel("0 x 0")
        self.source_colors_label = QLabel("0")
        source_layout.addRow("File:", self.source_filename_label)
        source_layout.addRow("Size:", self.source_dimensions_label)
        source_layout.addRow("Colors:", self.source_colors_label)
        source_group.setLayout(source_layout)
        
        # Add all sections to info pane
        info_layout.addWidget(project_group)
        info_layout.addWidget(pattern_group)
        info_layout.addWidget(stitches_group)
        info_layout.addWidget(source_group)
        info_layout.addStretch()  # Pushes everything up
        info_pane.setLayout(info_layout)
        
        # Update main layout to include info pane
        main_layout = QHBoxLayout()
        left_side = QWidget()
        left_side.setLayout(layout)  # Your existing layout
        main_layout.addWidget(left_side)
        main_layout.addWidget(info_pane)
        
        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

        # Move update_info_pane out of __init__
        
        # Create the status bar with image size info
        status_bar = QStatusBar()
        self.image_size_label = QLabel("Image size: ")
        status_bar.addWidget(self.image_size_label)
        self.setStatusBar(status_bar)

        #self.view_flosses_window()

        # For existing labels, set font:
        self.src_image_label.setFont(QFont("Verdana"))
        self.amended_image_label.setFont(QFont("Verdana"))
        self.picture_image_label.setFont(QFont("Verdana"))
        self.labelSrcTitle.setFont(QFont("Verdana"))
        self.labelSrcColours.setFont(QFont("Verdana"))
        self.labelSrcDimensions.setFont(QFont("Verdana"))

        # Add to stylesheet for consistent font across all widgets:
        self.setStyleSheet("""
            QWidget {
                font-family: Verdana;
            }
            QLabel {
                font-family: Verdana;
            }
            QPushButton {
                font-family: Verdana;
            }
            QLineEdit {
                font-family: Verdana;
            }
            QComboBox {
                font-family: Verdana;
            }
        """)

    def open_color_palette_window(self):
        """Open the color palette window"""
        if not hasattr(self, 'color_palette_window') or not self.color_palette_window.isVisible():
            self.color_palette_window = ColorPaletteWindow(
            main_window=self,
            processor=self.processor,
            brands=self.all_brands,
            project=self.project
        )
        self.color_palette_window.update_image_labels_signal.connect(self.update_image_labels)
        self.color_palette_window.show()

    def closeEvent(self, event):
        """Handle closing all child windows when main window closes"""
        # Close view flosses window
        if self._view_flosses_window:
            self._view_flosses_window.close()
            self._view_flosses_window = None

        # Close edit window
        if self.edit_window:
            self.edit_window.close()
            self.edit_window = None

        # Close assign flosses window
        if self.assign_flosses_window:
            self.assign_flosses_window.close()
            self.assign_flosses_window = None

        # Close motif window
        if self.motif_window:
            self.motif_window.close()
            self.motif_window = None

        # Close floss image dialog
        if hasattr(self, 'floss_image_dialog') and self.floss_image_dialog:
            self.floss_image_dialog.close()
            self.floss_image_dialog = None

        # Accept the close event
        event.accept()

    def show_settings(self):
        dialog = SettingsDialog(self.config, self)  # Now self.config exists
        if dialog.exec() == QDialog.Accepted:
            # Optionally refresh any UI elements that depend on settings
            self.update_image_labels()

    # Move update_info_pane here as a class method
    def update_info_pane(self):
        # Project info
        self.project_name_label.setText(self.project.projectName or "No project loaded")
        saved_time = self.project.dtProjectSaved.strftime("%Y-%m-%d %H:%M:%S") if self.project.dtProjectSaved else "Never saved"
        self.project_saved_label.setText(saved_time)
        
        # Pattern info
        generated_time = self.project.dtGenerated.strftime("%Y-%m-%d %H:%M:%S") if self.project.dtGenerated else "Not generated"
        self.pattern_generated_label.setText(generated_time)
        self.pattern_size_label.setText(f"{self.processor.iAmendedWidth} x {self.processor.iAmendedHeight}")

        # Set color for flosses label and value based on number of flosses
        if len(self.project.flosses) == 0:
            self.pattern_flosses_label.setStyleSheet("color: red;")
        else:
            self.pattern_flosses_label.setStyleSheet("color: black;")
        self.pattern_flosses_label.setText(str(len(self.project.flosses)))

        # Set color for motifs label and value based on selected motifs
        if not self.project.selected_motifs:
            self.pattern_motifs_label.setStyleSheet("color: red;")
            self.pattern_motifs_label.setText("None")
        else:
            self.pattern_motifs_label.setStyleSheet("color: black;")
            self.pattern_motifs_label.setText(f"{len(self.project.selected_motifs)} selected")
            tooltip_text = "\n".join(f"- {motif['name']}" for motif in self.project.selected_motifs)
            self.pattern_motifs_label.setToolTip(tooltip_text)

        self.pattern_stitch_types_label.setText(self.project.stitch_type)

        # Update stitch counts section with clearer formatting
        self.stitches_total_label.setText(str(self.project.stitch_counts.get_grand_total()))
        self.stitches_single_label.setText(str(self.project.stitch_counts.total_single))
        self.stitches_double_label.setText(str(self.project.stitch_counts.total_double))
        
        # Create detailed floss breakdown with better formatting
        floss_details = []
        for floss_key, counts in self.project.stitch_counts.floss_counts.items():
            floss_info = (
                f"{floss_key}\n"
                f"Color: {counts['color_name']}\n"
                f"Single: {counts['single']}\n"
                f"Double: {counts['double']}\n"
                f"Total: {counts['single'] + counts['double']}"
            )
            floss_details.append(floss_info)
        
        if floss_details:
            self.stitches_by_floss_label.setText(f"Using {len(self.project.stitch_counts.floss_counts)} flosses")
            self.stitches_by_floss_label.setToolTip("\n\n".join(floss_details))
        else:
            self.stitches_by_floss_label.setText("No stitches counted")
            self.stitches_by_floss_label.setToolTip("")

        # Source info
        self.source_filename_label.setText(Path(self.project.sFilenameSrc).name if self.project.sFilenameSrc else "No file loaded")
        self.source_dimensions_label.setText(f"{self.processor.iOriginalWidth} x {self.processor.iOriginalHeight}")
        self.source_colors_label.setText(str(self.processor.original_image_colours))
        
        # Update motifs display
        if not self.project.selected_motifs:
            self.pattern_motifs_label.setText("None")
        else:
            self.pattern_motifs_label.setText(f"{len(self.project.selected_motifs)} selected")
            
            # Create tooltip with motif details
            tooltip_text = "\n".join(f"- {motif['name']}" for motif in self.project.selected_motifs)
            self.pattern_motifs_label.setToolTip(tooltip_text)

    # Create the View Flosses Window    
    @Slot()
    def showMotifWindow(self):
        print("In MainWindow.showMotifWindow()")
        # Create a new instance of MotifEditor each time this method is called
        if self.motif_window is None:
            print("Creating new instance of MotifEditor")
            self.motif_window = MotifEditor(self.sm_config)
            self.motif_window.destroyed.connect(self.motif_window_closed)
            
            self.motif_window.result_signal.connect(self.handle_result)
        self.motif_window.show()

    @Slot()
    def motif_window_closed(self):
        print("In MainWindow.motif_window_closed()")
        self.motif_window = None

        # Create the assign flosses window
        #self.motifWindow = MotifEditor(self.sm_config)

    def show_view_flosses(self):
        """Show the view flosses window, creating it if necessary"""
        if not self._view_flosses_window:
            self._view_flosses_window = ViewFlossesWindow(self.all_brands, self.view_flosses_action)
        self._view_flosses_window.show()
        self._view_flosses_window.raise_()  # Bring window to front
    
    def generate_pattern(self):
        print("In MainWindow.generate_pattern()")
            
        # Show the generate options dialog
        dialog = GenerateOptionsDialog(self)
        if dialog.exec() == QDialog.Accepted:
            options = dialog.get_options()
            
            # Update the main window's stitch type if it changed
            self.stitch_type = options['stitch_type']
            
            # Generate the pattern with the selected options
            result = self.processor.generate_pattern(
                self.project, 
                self.config,
                self.project.selected_motifs, 
                options['stitch_type'],
                #self.use_colour,  # Pass the color flag
                options['pattern_color_mode']
            )
            
            if result is not None:
                # Extract pattern image and stitch collection from result
                pattern_image, stitch_collection = result
                self.project.stitch_collection = stitch_collection  # Store the stitch collection
                    
                self.set_image(pattern_image, self.picture_image_label)
                # Update generation timestamp and info pane
                self.project.dtGenerated = datetime.datetime.now()
                self.update_info_pane()
                
                # Convert stitch_collection to SVG content for all pattern types
                stitches_svg = []
                for stitch in stitch_collection.get_stitches():
                    start_x, start_y = stitch.start_point.x(), stitch.start_point.y()
                    end_x, end_y = stitch.end_point.x(), stitch.end_point.y()
                    
                    # Get color information from stitch metadata
                    rgb = stitch.metadata.get('rgb_color', (0, 0, 0))  # Default to black if no color
                    svg_color = f"rgb({rgb[0]},{rgb[1]},{rgb[2]})"
                    
                    stitch_line = (f'<line x1="{start_x}" y1="{start_y}" '
                                f'x2="{end_x}" y2="{end_y}" '
                                f'stroke="{svg_color}" stroke-width="{stitch.weight}"/>')
                    stitches_svg.append(stitch_line)
                
                # Join all stitch lines with newlines
                stitches_svg_content = '\n'.join(stitches_svg)
                
                # Generate single page pattern if requested
                if options['single_page']:
                    self.processor.generate_single_pattern(self.project, self.config, stitches_svg_content,options['single_color'])
                    
                    """# If single color patterns were also requested
                    if options['single_color']:
                        self.processor.generate_single_pattern_split_colours(
                            self.project,
                            stitch_collection,
                            self.processor.iAmendedWidth,
                            self.processor.iAmendedHeight
                        )"""
                
                # Generate split page pattern if requested
                if dialog.split_page_check.isChecked():
                    # Calculate number of pages based on dimensions
                    pages_width = math.ceil(self.processor.iAmendedWidth / options['split_width'])
                    pages_height = math.ceil(self.processor.iAmendedHeight / options['split_height'])
                    
                    # Generate split page patterns with SVG content
                    self.processor.generate_split_page_pattern(
                        self.project,
                        self.config,
                        stitches_svg_content,
                        pages_width,
                        pages_height,
                        options['single_color']
                    )
                    
                    """# If single color patterns were also requested
                    if options['single_color']:
                        # Generate split page patterns for each color
                        self.processor.generate_split_page_pattern_split_colours(
                            self.project,
                            stitch_collection,
                            pages_width,
                            pages_height
                        )"""
                    
            self.update_image_labels()

    def show_assign_flosses_window(self):
        """Create or reset the assign flosses window"""
        # If window exists, properly clean up
        if hasattr(self, 'assign_flosses_window') and self.assign_flosses_window:
            try:
                self.assign_flosses_window.close()
                self.assign_flosses_window.deleteLater()
            except:
                pass
            
        # Create new instance with current project state
        self.assign_flosses_window = AssignFlossesWindow(
            self.processor, 
            self.all_brands, 
            self.project
        )
        
        # Connect signals
        self.assign_flosses_window.update_image_labels_signal.connect(self.update_image_labels)
        self.assign_flosses_window.result_signal.connect(self.handle_result)
        
        # Connect window closed signal 
        self.assign_flosses_window.destroyed.connect(self.assign_flosses_window_closed)
        
        # Show the window
        self.assign_flosses_window.show()

    @Slot()
    def assign_flosses_window_closed(self):
        print("In MainWindow.assign_flosses_window_closed()")
        if hasattr(self, 'assign_flosses_window'):
            self.assign_flosses_window = None

    @Slot()  
    def show_edit_window(self):
        print("In MainWindow.show_edit_window()")
        # Create a new instance of EditImageWindow each time this method is called
        if self.edit_window is None:
            print("Creating new instance of EditImageWindow")
            self.edit_window = EditImageWindow(self.processor)
            self.edit_window.destroyed.connect(self.edit_window_closed)
            self.edit_window.update_image_labels_signal.connect(self.update_image_labels)
            self.edit_window.result_signal.connect(self.handle_result)
        self.edit_window.show()
        
    @Slot()
    def edit_window_closed(self):
        print("In MainWindow.edit_window_closed()")
        self.edit_window = None

    def newProject(self):
        # Reset all data first
        self.reset_project_data()

        project_name, ok = QInputDialog.getText(self, "New Project", "Enter project name:")
        if not ok or not project_name:
            return
            
        posDir = self.sm_config.posProjects_dir
        self.project.new_project(project_name, posDir)
        
        # Open file dialog to select source image
        src_image_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Source Image",
            str(self.sm_config.posHome_dir),
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif *.psd)"
        )
        
        if src_image_path:
            # Get the filename from the path
            src_filename = os.path.basename(src_image_path)
            
            # Create destination path in project folder
            dest_path = self.project.posProjectDir / src_filename
            
            # Copy the file to project directory
            shutil.copy2(src_image_path, dest_path)
            
            # Set the project's source filename
            self.project.sFilenameSrc = str(dest_path)
            
            # Continue with opening the image
            #self.open_image()
            self.processor.openImage(self.project.sFilenameSrc)
            #self.project.sFilenameSrc=sFilePath
            self.update_image_labels()
            print("Amended Image was updated")
            self.project.dtAmendedUpdated=datetime.datetime.now()

            # Need to set the project.filenameAmended to the filename of the source _SM
            #print("Src full_path: %s" % (str(sFilePath)))
            posFPath = Path(dest_path)
            self.project.sFilenameAmended=str(posFPath.stem) + "_Amended" + ".png"

            print("project.sFilenameAmended: %s" % (self.project.sFilenameAmended))
            self.update_generate_button_state()
            self.update_info_pane()

    def start_background_selection(self):
        """Start background color selection mode"""
        if not self.processor.image_loaded:
            return
            
        # Store original cursor
        self.old_cursor = self.cursor()
        
        # Set eyedropper cursor
        eyedropper = QCursor(QPixmap("eyedropper.png"))
        self.setCursor(eyedropper)
        
        # Connect mouse click event
        self.amended_image_label.mousePressEvent = self.pick_background_color

    def pick_background_color(self, event):
        """Handle mouse click to pick background color"""
        if event.button() == Qt.LeftButton:
            # Get click position relative to label
            pos = event.pos()
            
            # Convert position to image coordinates
            label_size = self.amended_image_label.size()
            image_size = self.processor.amended_image.shape
            
            # Calculate scaling factors
            scale_x = image_size[1] / label_size.width()
            scale_y = image_size[0] / label_size.height()
            
            # Get image coordinates
            image_x = int(pos.x() * scale_x)
            image_y = int(pos.y() * scale_y)
            
            # Get color at point (BGR format)
            bgr_color = self.processor.amended_image[image_y, image_x]
            
            # Convert to RGB
            rgb_color = (int(bgr_color[2]), int(bgr_color[1]), int(bgr_color[0]))
            
            # Store in project
            self.project.background_colour = rgb_color
            
            # Mark project as having unsaved changes
            self.project.bUnsavedChanges = True
                    
            # Update button color
            self.select_background_button.setStyleSheet(
                f"background-color: rgb({rgb_color[0]},{rgb_color[1]},{rgb_color[2]});"
                f"color: {'white' if sum(rgb_color) < 380 else 'black'};"
            )
            
            # Reset cursor
            self.setCursor(self.old_cursor)
            
            # Disconnect mouse event
            self.amended_image_label.mousePressEvent = None

    def reset_project_data(self):
        """Reset all project-related data to initial state"""
        # Reset project object
        self.project = Project()
        self.processor = ImageProcessor()
        self.processor.set_config(self.config)
        
        # Reset UI elements
        #self.color_input.clear()
        self.size_input.clear()
        
        # Reset motif selections
        self.project.selected_motifs = []
        self.project.flosses=[]
        
        # Reset stitch settings
        self.stitch_type = "Single"
        self.project.stitch_type = "Single"
        
        # Reset color output setting
        #self.use_colour = True
        #self.project.use_colour = True
        #self.colour_output_button.setText("Color Output: Yes")
        #self.colour_output_button.setChecked(True)
        
        # Clear image labels
        self.src_image_label.setText("No Image Opened")
        self.amended_image_label.setText("No Image Opened")
        self.picture_image_label.setText("No Pattern Generated")
        
        # Disable pattern generation
        self.generate_pattern_button.setEnabled(False)
        
        # Reset info pane
        self.update_info_pane()

    def openProject(self):
        # Reset all data first
        self.reset_project_data()

        print("In main.openProject()")
        sFilePath, _ = QFileDialog.getOpenFileName(
            self, 
            "Open Project", 
            str(self.sm_config.posHome_dir), 
            "Project Files (*.SMP)"
        )
        if sFilePath:
            print("openProject() File path = %s " % (sFilePath))
            self.project.open_project(sFilePath)

            # Open the source and amended image
            posAmendedFullPath = self.project.posProjectDir / self.project.sFilenameAmended
            posPictureFullPath = self.project.posProjectDir / self.project.sFilenamePicture
            if self.project.sFilenamePicture == "":
                posPictureFullPath = None
            print("srcImage: %s   amendedImage: %s   pictureImage: %s" % (
                str(self.project.sFilenameSrc), 
                str(posAmendedFullPath), 
                str(posPictureFullPath)
            ))
            self.processor.openImages(self.project.sFilenameSrc, posAmendedFullPath, posPictureFullPath)
               
            # Update background button color based on project's background color
            rgb_color = self.project.background_colour
            self.select_background_button.setStyleSheet(
                f"background-color: rgb({rgb_color[0]},{rgb_color[1]},{rgb_color[2]});"
                f"color: {'white' if sum(rgb_color) < 380 else 'black'};"
            )
                        
            # Update UI state
            self.update_image_labels()
            self.update_info_pane()
            self.update_generate_button_state()
            
            # Update color output button state
            #self.use_colour = self.project.use_colour
            #self.colour_output_button.setText(
            #    f"Color Output: {'Yes' if self.use_colour else 'No'}"
            #)
            #self.colour_output_button.setChecked(self.use_colour)

    def saveProject(self):
        print("In main.saveProject()")
        if self.project.projectName != "":
            self.project.save_project(self.sm_config.posProjects_dir, self.processor.amended_image)
            self.update_info_pane()

    def displayProject(self):
        title, detail = self.project.get_info()
        msg = QMessageBox() 
        msg.setIcon(QMessageBox.Information) 
        msg.setText(title) 
        msg.setInformativeText(detail) 
        msg.setWindowTitle("Project Information") 
        msg.setStandardButtons(QMessageBox.Ok) 
        msg.exec()

    def displayInfo(self, sWhat):
        global KNOWN_BUGS
        global KNOWN_ENHANCEMENTS
        global KNOWN_UI

        if sWhat == "Bugs":
            sText=KNOWN_BUGS
            sTitle="Known Bugs"
        elif sWhat == "UI":
            sText=KNOWN_UI
            sTitle="User Interface Improvements"
        elif sWhat=="Enh":
            sTitle="Planned Enhancements"
            sText=KNOWN_ENHANCEMENTS
        elif sWhat=="Next":
            sTitle="Next releases"
            sText=KNOWN_NEXT

        msg = QMessageBox() 
        msg.setIcon(QMessageBox.Information) 
        msg.setText(sTitle) 
        msg.setInformativeText(sText) 
        msg.setWindowTitle(sTitle) 
        msg.setStandardButtons(QMessageBox.Ok) 
        msg.exec()

    def export_pal_file(self):
        base, _ = os.path.splitext(self.project.sFilenameAmended)
        posFilename=self.project.posProjectDir
        new_filename = f"{base}{'.pal'}"
            
        posFilename=self.project.posProjectDir / new_filename
        print("posFilename: %s" % (str(posFilename)))

        textMap=""
        
        for floss in self.project.flosses:
            print("Brand: %s Name: %s Colour: %s" % (floss.brand, floss.name, floss.color_name))
            red,green,blue=floss.rgb
            textMap+=floss.name+","+str(red)+","+str(green)+","+str(blue)+"\n"
     
        with open(str(posFilename), "w") as f:
            f.writelines(textMap)

    # View Menu Methods
    def toggle_src_image(self, state):
        self.src_image_label.setVisible(state)
        self.labelSrcTitle.setVisible(state)
        self.labelSrcColours.setVisible(state)
        #self.adjustSize()

    def toggle_amended_image(self, state):
        self.amended_image_label.setVisible(state)
        self.labelAmendedTitle.setVisible(state)
        self.labelAmendedColours.setVisible(state)
        #self.adjustSize()

    def toggle_assign_flosses_window(self, state):
        self.assign_flosses_window.setVisible(state)
        self.set_image(self.processor.floss_image, self.floss_image_label)

    def toggle_view_flosses_window(self, state):
        """Toggle visibility of view flosses window"""
        if state:
            self.show_view_flosses()
        elif self._view_flosses_window:
            self._view_flosses_window.close()
            self._view_flosses_window = None

    @Slot()
    def update_image_labels(self):
        """Update all image labels, handling None cases"""
        if self.processor.original_image is not None:
            self.set_image(self.processor.original_image, self.src_image_label)
            
        if self.processor.amended_image is not None:
            self.set_image(self.processor.amended_image, self.amended_image_label)
            
        if self.processor.picture_image is not None:
            self.set_image(self.processor.picture_image, self.picture_image_label)
            
        # Update controls and info
        #self.color_input.setValidator(QIntValidator(1, self.processor.amended_image_colours))
        #self.color_input.setPlaceholderText(f"(1-{self.processor.amended_image_colours})")
        
        self.size_input.setValidator(QIntValidator(1, self.processor.iAmendedMaxStitches))
        self.size_input.setPlaceholderText(f"(1-{self.processor.iAmendedMaxStitches})")
        
        self.update_info_pane()
        self.update_generate_button_state()

    @Slot(bool)
    def handle_result(self, result):
        self.bUpdated=result
        if self.bUpdated==True: 
            print("Amended Image was updated")
            self.project.dtAmendedUpdated=datetime.datetime.now()

    def set_image(self, image, label):
        """Display an image in a QLabel, handling None case"""
        if image is None:
            label.setText("No Image")
            return

        # Make sure image data is contiguous
        if not image.flags['C_CONTIGUOUS']:
            image = np.ascontiguousarray(image)


        height, width, channel = image.shape
        bytes_per_line = 3 * width
        q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_BGR888)
        pixmap = QPixmap.fromImage(q_image)
        label.setPixmap(pixmap.scaled(label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
    def reduce_colors(self):
        print("In main.reduce_colors()")
        try:
            num_colors = int(self.color_input.text())
            if num_colors <= 0:
                raise ValueError("Number of colors must be positive")
                
            # Show processing cursor
            QApplication.setOverrideCursor(Qt.WaitCursor)
            
            # Attempt color reduction
            if self.processor.reduce_colors(num_colors):
                print("Amended Image was updated")
                self.project.dtAmendedUpdated = datetime.datetime.now()
                self.update_image_labels()
                
                # Refresh assign flosses window if it's open
                if self.assign_flosses_window is not None:
                    self.assign_flosses_window.refresh_colors(self.processor.amended_image)
                
                # Clear the input field
                self.color_input.clear()
            else:
                QMessageBox.warning(self, "Error", 
                    "Failed to reduce colors. Try a different number of colors.")
                
        except ValueError as e:
            QMessageBox.warning(self, "Invalid Input", 
                "Please enter a valid number of colors.")
        finally:
            # Restore cursor
            QApplication.restoreOverrideCursor()


    def reduce_size(self):
        print("In main.reduce_size()")
        try:
            max_size = int(self.size_input.text())
            self.processor.change_size(max_size)
            print("Amended Image was updated")
            self.project.dtAmendedUpdated = datetime.datetime.now()
            self.update_image_labels()
            # Clear the input field
            self.size_input.clear()
        except ValueError:
            QMessageBox.warning(self, "Invalid Input", "Please enter a valid number of stitches.")

    def undo(self):
        print("In main.undo()")
        self.processor.undo()
        print("Amended Image was updated :%d x %d" % (self.processor.iAmendedHeight, self.processor.iAmendedWidth))  
        self.project.dtAmendedUpdated=datetime.datetime.now()

        # Update image labels and info pane
        self.update_image_labels()
        self.update_info_pane()  # Add this line to refresh the info pane

    def redo(self):
        print("In main.redo()")
        self.processor.redo()
        print("Amended Image was updated :%d x %d" % (self.processor.iAmendedHeight, self.processor.iAmendedWidth))  
        self.project.dtAmendedUpdated=datetime.datetime.now()
        
        # Update image labels and info pane
        self.update_image_labels()
        self.update_info_pane()  # Add this line to refresh the info pane

    def toggle_pattern_window(self, checked): 
        if checked: self.pattern_window.show() 
        else: self.pattern_window.hide()
    
    def open_image(self):
        print("In main.open_image()")
        sFilePath, _ = QFileDialog.getOpenFileName(self, "Open Image", "", "Image Files (*.png *.jpg *.bmp)")
        if sFilePath:
            print("open_image() File path = %s " % (sFilePath))
            self.processor.openImage(sFilePath)
            self.project.sFilenameSrc=sFilePath
            self.update_image_labels()
            print("Amended Image was updated")
            self.project.dtAmendedUpdated=datetime.datetime.now()

            # Need to set the project.filenameAmended to the filename of the source _SM
            print("Src full_path: %s" % (str(sFilePath)))
            posFPath = Path(sFilePath)
            self.project.sFilenameAmended=str(posFPath.stem) + "_Amended" + ".png"

            print("project.sFilenameAmended: %s" % (self.project.sFilenameAmended))
            self.update_generate_button_state()

    def update_brands(self):
        self.floss_brands_window = FlossBrandsWindow(self.all_brands)
        self.floss_brands_window.show()

    def update_flosses(self):
        self.flosses_window = FlossesWindow(self.all_brands)
        self.flosses_window.show()

    def new_motif(self):
        self.motif_window = MotifEditor()
        self.motif_window.show()

    def choose_motifs(self):
        dialog = MotifSelectionDialog(self.sm_config.posMotifs_dir, self)
        if dialog.exec() == QDialog.Accepted:
            self.project.selected_motifs = dialog.get_selected_motifs()
            self.update_info_pane()
            self.update_generate_button_state()

    def update_motif_display(self):
        # Update the pattern_motifs_label in the info pane
        if not self.selected_motifs:
            self.pattern_motifs_label.setText("None")
            return
            
        # Create a widget to hold motif names and icons
        motif_widget = QWidget()
        motif_layout = QVBoxLayout(motif_widget)
        
        for motif in self.selected_motifs:
            # Create horizontal container for each motif
            motif_container = QWidget()
            h_layout = QHBoxLayout(motif_container)
            
            # Add name label
            name_label = QLabel(motif['name'])
            h_layout.addWidget(name_label)
            
            # Add small SVG preview
            svg_widget = QSvgWidget(motif['svg_file'])
            svg_widget.setFixedSize(20, 20)
            h_layout.addWidget(svg_widget)
            
            motif_layout.addWidget(motif_container)
        
        # Update the pattern_motifs_label
        self.pattern_motifs_label.setText(f"{len(self.selected_motifs)} selected")
        
        # Create tooltip with motif details
        tooltip_widget = QWidget()
        tooltip_layout = QVBoxLayout(tooltip_widget)
        for motif in self.selected_motifs:
            tooltip_layout.addWidget(QLabel(f"- {motif['name']}"))
        self.pattern_motifs_label.setToolTip(tooltip_widget.layout().itemAt(0).widget().text())

    def update_generate_button_state(self):
        print(type(self.project.dtFlossesAssigned))

        can_generate = ((self.processor.amended_image is not None) and 
                       (len(self.project.selected_motifs) > 0) and
                       (self.project.dtFlossesAssigned > datetime.datetime.min)) 
        self.generate_pattern_button.setEnabled(can_generate)
        print("can_generate: %d, amended: %d, motifs: %d, Flosses: %d " % (can_generate,(self.processor.amended_image is not None),(len(self.project.selected_motifs) > 0),(self.project.dtFlossesAssigned > datetime.datetime.min)))

    def toggle_color_output(self):
        """Toggle between colour and black & white output"""
        self.use_colour = not self.use_colour
        self.colour_output_button.setText(f"Colour Output: {'Yes' if self.use_colour else 'No'}")
        self.project.use_colour = self.use_colour
        print("main.toggle_color(). use_color: %d   project.useColour: %d" % (self.use_colour, self.project.use_colour))

    def show_stitch_weights_dialog(self):
        dialog = StitchWeightsDialog(self)
        if dialog.exec() == QDialog.Accepted:
            if dialog.single_radio.isChecked():
                self.stitch_type = "Single"
            elif dialog.double_radio.isChecked():
                self.stitch_type = "Double"
            else:
                self.stitch_type = "Single & Double"
            
            # Update project's stitch type
            self.project.stitch_type = self.stitch_type
            
            # Update info pane
            self.pattern_stitch_types_label.setText(self.stitch_type)
            self.update_info_pane()

    def crop_to_image(self):
        """Crops the amended image to remove white borders."""
        if self.processor.amended_image is None:
            return
            
        # Convert to grayscale for easier white detection
        gray = cv2.cvtColor(self.processor.amended_image, cv2.COLOR_BGR2GRAY)
        
        # Find non-white pixels
        # Using threshold slightly below 255 to account for anti-aliasing
        mask = gray < 250
        
        # Find coordinates of non-white pixels
        coords = np.argwhere(mask)
        
        if len(coords) == 0:  # Image is completely white
            return
            
        # Find bounding box
        y_min, x_min = coords.min(axis=0)
        y_max, x_max = coords.max(axis=0)
        
        # Add small padding (1 pixel)
        x_min = max(0, x_min - 1)
        y_min = max(0, y_min - 1)
        x_max = min(self.processor.amended_image.shape[1] - 1, x_max + 1)
        y_max = min(self.processor.amended_image.shape[0] - 1, y_max + 1)
        
        # Crop the image
        cropped = self.processor.amended_image[y_min:y_max+1, x_min:x_max+1]
        
        # Update processor's amended image
        self.processor.amended_image = cropped
        
        # Add to history for undo/redo
        self.processor.history.append(self.processor.amended_image.copy())
        self.processor.future.clear()
        
        # Update dimensions
        self.processor.iAmendedHeight, self.processor.iAmendedWidth = cropped.shape[:2]
        self.processor.iAmendedMaxStitches = max(self.processor.iAmendedWidth, self.processor.iAmendedHeight)
        
        # Update amended image colors
        self.processor.amended_image_colours = self.processor.get_number_of_colors(cropped)
        
        # Update timestamp
        self.project.dtAmendedUpdated = datetime.datetime.now()
        
        # Update display
        self.update_image_labels()

    def show_project_data(self):
        """Show dialog to select and view project data"""
        sFilePath, _ = QFileDialog.getOpenFileName(
            self, 
            "Open Project", 
            str(self.sm_config.posHome_dir), 
            "Project Files (*.SMP)"
        )
        
        if sFilePath:
            try:
                # Load the project
                with open(sFilePath, 'rb') as file:
                    loaded_data = pickle.load(file)
                    
                # Create dictionary of project info with formatted values
                project_info = {
                    "Project Name": loaded_data["projectName"],
                    "Project Directory": loaded_data["projectDir"],
                    "Pattern Directory": loaded_data["patternDir"],
                    "Pattern Full Directory": loaded_data["patternFullDir"],
                    "Pattern Full SC Directory": loaded_data["patternFullSCDir"],
                    "Pattern Page Directory": loaded_data["patternPageDir"],
                    "Pattern Page SC Directory": loaded_data["patternPageSCDir"],
                    "Source Filename": loaded_data["filenameSrc"],
                    "Amended Filename": loaded_data["filenameAmended"],
                    "Picture Filename": loaded_data["filenamePicture"],
                    "Project Saved": loaded_data["projectSaved"].strftime('%Y-%m-%d %H:%M:%S') if loaded_data["projectSaved"] else "Never",
                    "Amended Updated": loaded_data["amendedUpdated"].strftime('%Y-%m-%d %H:%M:%S') if loaded_data["amendedUpdated"] else "Never",
                    "Flosses Assigned Date": loaded_data["flossesAssigned"].strftime('%Y-%m-%d %H:%M:%S') if loaded_data["flossesAssigned"] else "Never",
                    "Flosses": loaded_data["flosses"],
                    "Assigned Flosses": loaded_data["assigned_flosses"],
                    "Selected Motifs": loaded_data.get("selected_motifs", []),
                    "Stitch Type": loaded_data.get("stitch_type", "Not set"),
                    "Use Colour": "Yes" if loaded_data.get("use_colour", False) else "No",
                    "Generated Date": loaded_data.get("generated", datetime.datetime.min).strftime('%Y-%m-%d %H:%M:%S'),
                    "Stitch Counts": loaded_data.get("stitch_counts", "Not available"),
                    "Background Colour": loaded_data.get("background_colour", "Not set"),
                    "Pattern File Format": loaded_data.get("pattern_file_format", "pdf"),
                    "Picture File Format": loaded_data.get("picture_file_format", "pdf")
                }
                
                # Create and show dialog
                dialog = QDialog(self)
                dialog.setWindowTitle("Project Data")
                dialog.setMinimumWidth(600)
                
                layout = QVBoxLayout(dialog)
                
                # Create scroll area
                scroll = QScrollArea()
                scroll.setWidgetResizable(True)
                scroll_content = QWidget()
                scroll_layout = QFormLayout(scroll_content)
                
                # Add project information
                for key, value in project_info.items():
                    name_label = QLabel(key)
                    name_label.setFont(QFont("Verdana", 10, QFont.Bold))
                    
                    if isinstance(value, list):
                        if not value:
                            value_str = "None"
                        else:
                            value_str = '\n'.join([f"• {item}" for item in value])
                    elif isinstance(value, dict):
                        if not value:
                            value_str = "Empty"
                        else:
                            value_str = '\n'.join([f"• {k}: {v}" for k, v in value.items()])
                    else:
                        value_str = str(value)
                    
                    value_label = QLabel(value_str)
                    value_label.setFont(QFont("Verdana", 10))
                    value_label.setWordWrap(True)
                    
                    scroll_layout.addRow(name_label, value_label)
                
                scroll.setWidget(scroll_content)
                layout.addWidget(scroll)
                
                # Add OK button
                buttons = QDialogButtonBox(QDialogButtonBox.Ok)
                buttons.accepted.connect(dialog.accept)
                layout.addWidget(buttons)
                
                dialog.exec()
                
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load project: {str(e)}")

class MotifSelectionDialog(QDialog):
    def __init__(self, motifs_dir, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Select Motifs")
        self.setModal(True)
        self.motifs_dir = Path(motifs_dir)
        self.selected_motifs = []
        
        # Create layout
        layout = QVBoxLayout(self)
        
        # Create scroll area for motifs
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        self.grid_layout = QGridLayout(scroll_content)
        
        # Load and display motifs
        self.load_motifs()
        
        scroll.setWidget(scroll_content)
        layout.addWidget(scroll)
        
        # Add OK/Cancel buttons
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setMinimumSize(600, 400)

    def load_motifs(self):
        mtf_files = list(self.motifs_dir.glob("*.MTF"))
        row = 0
        col = 0
        max_cols = 3  # Number of motifs per row
        
        for mtf_file in mtf_files:
            svg_file = self.motifs_dir / f"{mtf_file.stem}.svg"
            if svg_file.exists():
                container = QWidget()
                container_layout = QVBoxLayout(container)
                
                # Create checkbox with motif name
                checkbox = QCheckBox(mtf_file.stem)
                container_layout.addWidget(checkbox)
                
                # Create SVG preview
                svg_widget = QSvgWidget(str(svg_file))
                svg_widget.setFixedSize(100, 100)
                container_layout.addWidget(svg_widget)
                
                self.grid_layout.addWidget(container, row, col)
                
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1

    def get_selected_motifs(self):
        selected = []
        for i in range(self.grid_layout.count()):
            widget = self.grid_layout.itemAt(i).widget()
            checkbox = widget.findChild(QCheckBox)
            if checkbox and checkbox.isChecked():
                motif_name = checkbox.text()
                mtf_file = self.motifs_dir / f"{motif_name}.MTF"
                svg_file = self.motifs_dir / f"{motif_name}.svg"
                selected.append({
                    'name': motif_name,
                    'mtf_file': str(mtf_file),
                    'svg_file': str(svg_file)
                })
        return selected

class ProjectDataDialog(QDialog):
    def __init__(self, project_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Project Data")
        self.setMinimumWidth(600)
        
        layout = QVBoxLayout(self)
        
        # Create a scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QFormLayout(scroll_content)
        
        # Add all project attributes
        for attr_name, attr_value in vars(project_data).items():
            if not attr_name.startswith('_'):  # Skip private attributes
                # Format the value for better readability
                if isinstance(attr_value, (list, dict)):
                    value_str = json.dumps(attr_value, indent=2)
                elif isinstance(attr_value, Path):
                    value_str = str(attr_value)
                else:
                    value_str = str(attr_value)
                
                # Create label with attribute name
                name_label = QLabel(attr_name)
                name_label.setFont(QFont("Verdana", 10, QFont.Bold))
                
                # Create text display for value
                value_text = QLabel(value_str)
                value_text.setFont(QFont("Verdana", 10))
                value_text.setWordWrap(True)
                
                # Add to layout
                scroll_layout.addRow(name_label, value_text)
        
        scroll.setWidget(scroll_content)
        layout.addWidget(scroll)
        
        # Add OK button
        buttons = QDialogButtonBox(QDialogButtonBox.Ok)
        buttons.accepted.connect(self.accept)
        layout.addWidget(buttons)

# Function to check if an object is a list or an instance of MyClass
def check_object_type(obj):
    if isinstance(obj, list):
        print(f'The object {obj} is a list.')
    elif isinstance(obj, br.Brand):
        print(f'The object {obj} is an instance of Brand.')
    elif isinstance(obj, br.Brands):
        print(f'The object {obj} is an instance of Brands.')        
    else:
        print(f'The object {obj} is neither a list nor an instance of Brand or Brands.')

def display_tileset(tileset):
    """Display all tiles in a tileset using OpenCV"""
    if not tileset.has_content():
        print(f"Tileset ({tileset.x}, {tileset.y}) has no content")
        return
        
    windows = []  # Keep track of window names
    
    # Calculate grid layout
    tiles_per_row = 3
    num_tiles = len(tileset.tiles)
    num_rows = (num_tiles + tiles_per_row - 1) // tiles_per_row
    
    # Position windows in a grid
    window_width = 120  # 100px image + 20px padding
    window_height = 120
    
    for i, tile in enumerate(tileset.tiles):
        # Calculate window position
        row = i // tiles_per_row
        col = i % tiles_per_row
        x_pos = col * window_width
        y_pos = row * window_height
        
        # Create white image for this tile
        display_size = 100
        scale = display_size // 10
        image = np.full((display_size, display_size, 3), 255, dtype=np.uint8)
        
        # Draw all stitches
        for stitch in tile.stitches:
            # Scale coordinates up
            start_x = int(stitch.stitchStart.x() * scale)
            start_y = int(stitch.stitchStart.y() * scale)
            end_x = int(stitch.stitchEnd.x() * scale)
            end_y = int(stitch.stitchEnd.y() * scale)
            
            # Draw line with scaled thickness
            cv2.line(
                image,
                (start_x, start_y),
                (end_x, end_y),
                (0, 0, 0),  # Black color
                thickness=stitch.weight * scale
            )
        
        # Add text showing tile info
        text = f"Tile {i} ({tile.x},{tile.y}) I:{tile.intensity}"
        cv2.putText(
            image,
            text,
            (5, 15),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.5,
            (0, 0, 255),  # Red color
            1
        )
        
        # Create and position window
        window_name = f"Tileset ({tileset.x},{tileset.y}) - {tile.motif_name}"
        cv2.namedWindow(window_name)
        cv2.moveWindow(window_name, x_pos, y_pos)
        cv2.imshow(window_name, image)
        windows.append(window_name)
    
    # Wait for single keypress to close all windows
    cv2.waitKey(0)
    
    # Close all windows
    for window in windows:
        cv2.destroyWindow(window)

def create_motif_map(motif_dicts, stitch_type="Single"):
    """Creates a map of tilesets from the provided motif dictionaries."""
    motif_map = MotifMap()
    loaded_motifs = []
    
    # First pass: load motifs and determine dimensions
    for motif_dict in motif_dicts:
        motif = Motif()
        if motif.load_motif(Path(motif_dict['mtf_file'])):
            loaded_motifs.append(motif)
            motif_map.width = max(motif_map.width, motif.tilesWide)
            motif_map.height = max(motif_map.height, motif.tilesHigh)
    
    # Second pass: create tiles and assign stitches
    for motif in loaded_motifs:
        for layer_idx, layer in enumerate(motif.layers):
            # Create tiles for this layer
            layer_tiles = {}
            
            # For Single & Double, we'll need two sets of tiles
            double_layer_tiles = {}
            
            # Initialize tiles
            for tileX in range(motif.tilesWide):
                for tileY in range(motif.tilesHigh):
                    layer_tiles[(tileX, tileY)] = Tile(tileX, tileY, layer_idx + 1, motif.name)
                    if stitch_type == "Single & Double":
                        double_layer_tiles[(tileX, tileY)] = Tile(tileX, tileY, layer_idx + 1, motif.name)

            # Assign stitches to tiles
            for stitch in layer.stitches:
                # Calculate stitch boundaries
                vecMinX = min(stitch.stitchStart.x(), stitch.stitchEnd.x()) / 10
                vecMaxX = max(stitch.stitchStart.x(), stitch.stitchEnd.x()) / 10
                vecMinY = min(stitch.stitchStart.y(), stitch.stitchEnd.y()) / 10
                vecMaxY = max(stitch.stitchStart.y(), stitch.stitchEnd.y()) / 10
                
                for tileRangeX in range(motif.tilesWide):  
                    for tileRangeY in range(motif.tilesHigh):  
                        if (tileRangeX, tileRangeY) in layer_tiles:
                            # Check if stitch belongs in this tile
                            if (tileRangeX <= vecMinX <= vecMaxX <= tileRangeX + 1 and
                                tileRangeY <= vecMinY <= vecMaxY <= tileRangeY + 1):
                                    
                                relative_start = QPointF(
                                    stitch.stitchStart.x() - (tileRangeX * 10),
                                    stitch.stitchStart.y() - (tileRangeY * 10)
                                )
                                relative_end = QPointF(
                                    stitch.stitchEnd.x() - (tileRangeX * 10),
                                    stitch.stitchEnd.y() - (tileRangeY * 10)
                                )
                                
                                if stitch_type == "Single":
                                    # Single weight only
                                    single_stitch = Stitch(relative_start, relative_end, stitch.weight//2)
                                    layer_tiles[(tileRangeX, tileRangeY)].add_stitch(single_stitch)
                                    
                                elif stitch_type == "Double":
                                    # Double weight only
                                    double_stitch = Stitch(relative_start, relative_end, stitch.weight)
                                    layer_tiles[(tileRangeX, tileRangeY)].add_stitch(double_stitch)
                                    
                                elif stitch_type == "Single & Double":
                                    # Add both single and double weight versions
                                    single_stitch = Stitch(relative_start, relative_end, stitch.weight//2)
                                    double_stitch = Stitch(relative_start, relative_end, stitch.weight)
                                    
                                    layer_tiles[(tileRangeX, tileRangeY)].add_stitch(single_stitch)
                                    double_layer_tiles[(tileRangeX, tileRangeY)].add_stitch(double_stitch)
            
            # Add tiles to tilesets
            if stitch_type == "Single & Double":
                # Add both single and double weight tiles
                for (x, y), tile in layer_tiles.items():
                    if tile.stitches:
                        tile.calculate_intensity()
                        double_tile = double_layer_tiles[(x, y)]
                        double_tile.calculate_intensity()
                        
                        tileset = motif_map.get_tileset(x, y)
                        tileset.add_tile(tile)  # Add single weight tile
                        tileset.add_tile(double_tile)  # Add double weight tile
            else:
                # Add single or double weight tiles as before
                for (x, y), tile in layer_tiles.items():
                    if tile.stitches:
                        tile.calculate_intensity()
                        tileset = motif_map.get_tileset(x, y)
                        tileset.add_tile(tile)
    
    return motif_map

def open_svg(svg_path):
    print(f"Opening SVG: {svg_path}")
    drawing = svg2rlg(str(svg_path))
    # Convert to PNG in memory
    png_data = renderPM.drawToString(drawing, fmt='PNG')
    # Convert to numpy array for OpenCV
    nparr = np.frombuffer(png_data, np.uint8)
    png_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    return png_image

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setFont(QFont("Verdana"))
    
    allBrands=br.Brands()
    print(len(allBrands.brands))
    allBrands.load_all_brands()
    print(len(allBrands.brands))
    
    allBrands.printBrands("Summary")
    config = SMConfig()
    window = MainWindow(allBrands,config)
    window.show()
    sys.exit(app.exec())
